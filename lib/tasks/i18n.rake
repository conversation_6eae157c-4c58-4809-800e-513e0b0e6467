# frozen_string_literal: true

namespace :i18n do
  desc "Validate I18n translations for completeness and consistency"
  task validate: :environment do
    require_relative "../i18n_validator"

    puts "🌍 Validating I18n translations..."
    puts "Available locales: #{I18n.available_locales.join(', ')}"
    puts "-" * 50

    result = I18nValidator.validate!

    if result[:valid]
      puts "✅ All translations are valid!"
    else
      puts "❌ Translation validation failed!"
    end

    puts "\n📊 Summary:"
    puts "  Locales checked: #{result[:summary][:locales_checked]}"
    puts "  Total errors: #{result[:summary][:total_errors]}"
    puts "  Total warnings: #{result[:summary][:total_warnings]}"
    puts "  Status: #{result[:summary][:status]}"

    if result[:errors].any?
      puts "\n🚨 Errors:"
      result[:errors].each_with_index do |error, index|
        puts "  #{index + 1}. #{error}"
      end
    end

    if result[:warnings].any?
      puts "\n⚠️  Warnings:"
      result[:warnings].each_with_index do |warning, index|
        puts "  #{index + 1}. #{warning}"
      end
    end

    exit(1) unless result[:valid]
  end

  desc "Show translation coverage for each locale"
  task coverage: :environment do
    require_relative "../i18n_validator"

    puts "📈 Translation Coverage Report"
    puts "=" * 50

    I18n.available_locales.each do |locale|
      coverage = I18nValidator.translation_coverage(locale)

      puts "\n🌍 Locale: #{locale}"
      puts "  Total keys: #{coverage[:total_keys]}"
      puts "  Translated keys: #{coverage[:translated_keys]}"
      puts "  Coverage: #{coverage[:coverage_percentage]}%"

      # Visual progress bar
      bar_length = 30
      filled_length = (coverage[:coverage_percentage] / 100.0 * bar_length).round
      bar = "█" * filled_length + "░" * (bar_length - filled_length)
      puts "  Progress: [#{bar}] #{coverage[:coverage_percentage]}%"
    end
  end

  desc "Find missing translation keys"
  task missing: :environment do
    require_relative "../i18n_validator"

    puts "🔍 Missing Translation Keys"
    puts "=" * 50

    missing_keys = I18nValidator.find_missing_keys

    if missing_keys.all? { |_, keys| keys.empty? }
      puts "✅ No missing keys found!"
    else
      missing_keys.each do |locale, keys|
        next if keys.empty?

        puts "\n🌍 Missing in #{locale}:"
        keys.each do |key|
          puts "  - #{key}"
        end
      end
    end
  end

  desc "Check if a specific translation key exists"
  task :check, [ :key, :locale ] => :environment do |_, args|
    require_relative "../i18n_validator"

    key = args[:key]
    locale = args[:locale]&.to_sym || I18n.locale

    if key.blank?
      puts "❌ Please provide a translation key"
      puts "Usage: rake i18n:check[common.actions.add,en]"
      exit(1)
    end

    puts "🔍 Checking translation key: '#{key}' in locale '#{locale}'"
    puts "-" * 50

    if I18nValidator.check_key_exists(key, locale)
      translation = I18n.t(key, locale: locale)
      puts "✅ Key exists!"
      puts "Translation: '#{translation}'"
    else
      puts "❌ Key does not exist in locale '#{locale}'"

      # Check if it exists in other locales
      other_locales = I18n.available_locales - [ locale ]
      existing_locales = other_locales.select { |l| I18nValidator.check_key_exists(key, l) }

      if existing_locales.any?
        puts "\n💡 Key exists in other locales:"
        existing_locales.each do |l|
          translation = I18n.t(key, locale: l)
          puts "  #{l}: '#{translation}'"
        end
      end
    end
  end

  desc "Generate missing translation keys template"
  task :generate_missing, [ :locale ] => :environment do |_, args|
    require_relative "../i18n_validator"

    target_locale = args[:locale]&.to_sym

    if target_locale.blank?
      puts "❌ Please provide a target locale"
      puts "Usage: rake i18n:generate_missing[id]"
      exit(1)
    end

    unless I18n.available_locales.include?(target_locale)
      puts "❌ Locale '#{target_locale}' is not available"
      puts "Available locales: #{I18n.available_locales.join(', ')}"
      exit(1)
    end

    puts "📝 Generating missing translation keys for locale: #{target_locale}"
    puts "-" * 50

    missing_keys = I18nValidator.find_missing_keys[:en] || []

    if missing_keys.empty?
      puts "✅ No missing keys found!"
    else
      puts "# Missing translations for #{target_locale}"
      puts "# Add these to config/locales/#{target_locale}.yml"
      puts

      missing_keys.each do |key|
        begin
          english_translation = I18n.t(key, locale: :en)
          puts "# #{key}: \"#{english_translation}\""
          puts "#{key}: \"TODO: Translate '#{english_translation}'\""
        rescue I18n::MissingTranslationData
          puts "#{key}: \"TODO: Add translation\""
        end
      end
    end
  end

  desc "List all translation keys"
  task list: :environment do
    require_relative "../i18n_validator"

    puts "📋 All Translation Keys"
    puts "=" * 50

    I18n.available_locales.each do |locale|
      puts "\n🌍 Locale: #{locale}"
      validator = I18nValidator.new
      keys = validator.send(:extract_all_keys, locale)

      keys.sort.each do |key|
        begin
          translation = I18n.t(key, locale: locale, raise: true)
          if translation.is_a?(String)
            puts "  #{key}: \"#{translation.truncate(50)}\""
          else
            puts "  #{key}: [Complex value]"
          end
        rescue I18n::MissingTranslationData
          puts "  #{key}: [MISSING]"
        end
      end
    end
  end

  desc "Sync translation keys across locales"
  task sync: :environment do
    puts "🔄 This task would sync translation keys across locales"
    puts "⚠️  This is a placeholder - implement based on your needs"
    puts "Consider using tools like i18n-tasks gem for advanced syncing"
  end
end
