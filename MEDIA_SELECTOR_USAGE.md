# Media Selector Modal Usage

This document explains how to use the WordPress-style media selector modal in your Rails application.

## Overview

The media selector modal provides a WordPress-like interface for selecting images from your media library or uploading new ones. It's already integrated into the post form for featured image selection.

## Features

- **Browse existing media**: View all uploaded media items in a grid layout
- **Upload new files**: Upload new images directly from the modal
- **Search and filter**: Search by filename and filter by media type
- **Image-only mode**: Restrict selection to images only
- **Responsive design**: Works on desktop and mobile devices

## How It Works

### 1. Post Form Integration

The featured image section in your post form (`app/views/posts/_form.html.erb`) now includes:

- A preview area showing the currently selected image
- A "Select Image" button that opens the media selector modal
- A "Remove" button to clear the selection
- A hidden input field that stores the selected media item ID

### 2. Media Selector Modal

The modal (`app/views/partials/dashboard/media_selector_modal.html.erb`) provides:

- Upload section for new files
- Search and filter controls
- Grid view of existing media items
- Click-to-select functionality

### 3. JavaScript Controllers

Two Stimulus controllers handle the functionality:

#### `featured-image-picker-controller.js`
- Manages the featured image preview and selection
- Opens the media selector modal
- Handles media selection events
- Updates the preview when an image is selected

#### `media-selector-controller.js`
- Loads media items from the API
- Handles file uploads
- Manages search and filtering
- Dispatches selection events

## Usage in Other Forms

To use the media selector in other forms, follow this pattern:

### 1. Add the form field with controller

```erb
<div data-controller="featured-image-picker" 
     data-featured-image-picker-modal-id-value="my-media-selector"
     data-featured-image-picker-input-name-value="model[media_item_id]">
  
  <%= form.hidden_field :media_item_id, data: { "featured-image-picker-target": "input" } %>
  
  <div data-featured-image-picker-target="preview">
    <!-- Preview content -->
  </div>
  
  <button type="button" 
          class="btn btn-primary"
          data-action="click->featured-image-picker#openModal">
    Select Media
  </button>
</div>
```

### 2. Include the modal partial

```erb
<%= render 'partials/dashboard/media_selector_modal',
    modal_id: 'my-media-selector',
    target_input: 'model[media_item_id]',
    images_only: true,
    title: 'Select Media' %>
```

### 3. Parameters

- `modal_id`: Unique ID for the modal
- `target_input`: Name of the form field to populate
- `images_only`: Set to `true` to show only images
- `title`: Modal title text

## API Endpoints

The media selector uses these endpoints:

- `GET /dashboard/media.json` - List media items with search/filter
- `POST /dashboard/media/upload` - Upload new media files
- `GET /dashboard/media/:id.json` - Get single media item details

## Styling

The modal uses Tailwind CSS classes and DaisyUI components. The design is responsive and follows your existing dashboard theme.

## File Upload

The upload functionality:

1. Validates file types (images only if `images_only` is true)
2. Shows upload progress
3. Automatically selects the uploaded file
4. Refreshes the media grid
5. Closes the modal

## Events

The system uses custom events for communication:

- `media-selector:mediaSelected` - Fired when a media item is selected
  - `detail.media` - Selected media object
  - `detail.targetInput` - Target input field name

## Error Handling

The modal handles various error states:

- Network errors during loading
- Upload failures
- Empty media library
- Search with no results

## Performance

- Media items are loaded via AJAX
- Images use thumbnail URLs for faster loading
- Search is debounced to reduce API calls
- Caching is implemented at the controller level
