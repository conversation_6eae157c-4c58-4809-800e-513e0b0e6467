# RORSCHOOLS

## Ruby version
- ruby-3.4.5

## System dependencies
- ### [Active Storage](https://guides.rubyonrails.org/active_storage_overview.html#requirements)
    - in ubuntu install libvips
    - `sudo apt install -y libvips42 libvips-dev`
    - or use ImageMagick
    - `config.active_storage.variant_processor :mini_magick`
- ### [TipTap](https://tiptap.dev/docs/editor/getting-started/install/vanilla-javascript)
    - `pnpm install @tiptap/core @tiptap/starter-kit @tiptap/extension-image @tiptap/extension-text-align @tiptap/extension-link @tiptap/extension-placeholder`
## Configuration
- ### Breadcrumbs
    - Adding a `before_action` callback
    - Calling `helpers.add_breadcrumb "Name", path` in controller methods
    - eg 
```
class MyController < ApplicationController
  before_action :set_breadcrumbs

  private

  def set_breadcrumbs
    helpers.add_breadcrumb "Dashboard", dashboard_root_path
    helpers.add_breadcrumb "My Section", my_section_path
  end
end
```

## Database creation

## Database initialization

## How to run the test suite

First, prepare your test database. This command will create the database and load the schema.
```bash
bin/rails db:test:prepare
```

You can run different parts of the test suite with the following commands:

*   **Run all tests (unit, integration, etc.):** `bin/rails test`
*   **Run system tests (using a browser):** `bin/rails test:system`
*   **Run a specific test file:** `bin/rails test path/to/your_test_file.rb`


## Services (job queues, cache servers, search engines, etc.)

## Deployment instructions

## ...

## config/sitemap.rb
```
SitemapGenerator::Sitemap.create do
  Post.published.find_each do |post|
    add post_path(post), 
        lastmod: post.updated_at,
        changefreq: 'weekly',
        priority: 0.8,
        images: post.featured_image.attached? ? [{ loc: url_for(post.featured_image), title: post.title }] : []
  end
end
```
```
bin/rails sitemap:install        # creates config/sitemap.rb task hooks if needed
bin/rails sitemap:refresh
```

`bundle exec rake i18n:coverage`

## Rails Pulse

### Manual Cleanup Operations

**Run cleanup manually:**
```bash
rails rails_pulse:cleanup
```

**Check current database status:**
```bash
rails rails_pulse:cleanup_stats
```

**Schedule automated cleanup:**
```ruby
RailsPulse::CleanupJob.perform_later
```