Rails.application.routes.draw do
  scope "/(:locale)", locale: /#{I18n.available_locales.join('|')}/ do
    root "home_page#index"
    get "/access_denied", to: "errors#access_denied", as: :access_denied
    resources :posts, only: [ :index, :show ], param: :slug
    resources :categories, only: [ :index, :show ], param: :slug
    resources :tags, only: [ :index, :show ], param: :slug

    scope :dashboard, as: :dashboard do
      root to: "dashboard_page#index"
      get "settings", to: "settings#index"
      get "analytics_data", to: "dashboard_page#analytics_data"
      patch "settings", to: "settings#update"
      get "posts", to: "posts#dashboard_list"
      get "categories", to: "categories#dashboard_list"
      get "tags", to: "tags#dashboard_list"
      get "pages", to: "pages#dashboard_list"
      mount RailsPulse::Engine => "/rails_pulse"

      resources :posts, param: :slug, except: [ :index, :show ]
      resources :categories, param: :slug, except: [ :index, :show ]
      resources :tags, param: :slug, except: [ :index, :show ]
      resources :permissions, only: [ :index, :create, :destroy ]
      resources :pages, param: :slug, except: [ :index, :show ]
      # Menu Management
      resources :menus do
        resources :menu_items, only: [ :create, :update, :destroy ] do
          collection do
            patch :reorder
          end
        end
      end
      # Media Library
      resources :media_library, path: "media", except: [ :new, :edit ], controller: "dashboard/media_library" do
        collection do
          post :upload
        end
      end

      # User Management
      get "users", to: "dashboard_page#users"
      post "users", to: "dashboard_page#create_user", as: "d_users"
      patch "users/:id", to: "dashboard_page#update_user", as: "d_user"
      delete "users/:id", to: "dashboard_page#delete_user"
    end

    devise_for :users, path: "auth", path_names: { sign_in: "login", sign_out: "logout", password: "secret", confirmation: "verification", unlock: "unblock" }, skip: [ :registrations ]
    as :user do
      get "users/edit" => "users/registrations#edit", :as => "edit_user_registration"
      put "users" => "users/registrations#update", :as => "user_registration"
    end

    # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

    # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
    # Can be used by load balancers and uptime monitors to verify that the app is live.
    get "up" => "rails/health#show", as: :rails_health_check

    # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
    # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
    # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

    # Defines the root path route ("/")
    # root "posts#index"
    #
    post "/uploads", to: "uploads#create"
  end
end
