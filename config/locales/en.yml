# Files in the config/locales directory are used for internationalization and
# are automatically loaded by Rails. If you want to use locales other than
# English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more about the API, please read the Rails Internationalization guide
# at https://guides.rubyonrails.org/i18n.html.
#
# Be aware that YAML interprets the following case-insensitive strings as
# booleans: `true`, `false`, `on`, `off`, `yes`, `no`. Therefore, these strings
# must be quoted to be interpreted as strings. For example:
#
#     en:
#       "yes": yup
#       enabled: "ON"

en:
  # Application-wide common translations
  common:
    actions:
      add: "Add %{model}"      
      show: "Show"
      edit: "Edit"
      delete: "Delete"
      save: "Save"
      cancel: "Cancel"
      back: "Back"
      refresh: "Refresh"
    confirmations:
      select: "Select"
      are_you_sure: "Are you sure?"
      delete_confirmation: "Are you sure you want to delete this %{item}?"
    responses:
      yes: "Yes"
      no: "No"
    status:
      published: "Published"
      draft: "Draft"
      active: "Active"
      inactive: "Inactive"
    fields:
      name: "Name"
      title: "Title"
      slug: "Slug"
      email: "Email"
      role: "Role"
      author: "Author"
      actions: "Actions"
      created_at: "Created at"
      updated_at: "Updated at"
      description: "Description"
    confirm:
      sure: "Are you sure?"
      sure2: "You are about to delete"
      sure3: "This action cannot be undone."

  # Navigation and layout
  navigation:
    sidebar:
      view_site: "View site"
      content: "Content"      

  # Page-specific translations
  pages:    
    dashboard:
      index:
        title: "Dashboard"
      pageviews:
        title: "Pageviews"
      top_pages:
        title: "Top Pages"
      page_path:
        title: "Page Path"
      rails_pulse:
        title: "Rails Pulse"
    permission:
      index:
        title: "Manage Permissions"
    menus:
      index:
        title: "Menus"     
      show:
        reorder_instruction: "Drag and drop to reorder, save on release."
        parent_menu_recommendation: "Parent menu recommended as custom URL and set url to #"
    settings:
      index:
        title: "Settings"
        site_name: "Site Name"
        site_description: "Site Description"
        site_light_theme: "Site Light Theme"
        site_dark_theme: "Site Dark Theme"
        site_default_locale: "Site Default Language"
        light_theme: "Light Theme"
        dark_theme: "Dark Theme"
        site_default_time_zone: "Site Default Time Zone"
        time_zone: "Time Zone"
        user_settings: "User Settings"
        site_settings: "Site Settings"
        pick_light_theme: "Pick a light theme"
        pick_dark_theme: "Pick a dark theme"
        save_site_settings: "Save Site Settings"
        save_user_settings: "Save User Settings"
        footer_name: "Footer Name"
        footer_year: "Footer Year"
        footer_desc: "Footer Description"
    errors:
      not_found:
        title: "404 Not Found"
        heading: "Uh-oh!"
        message: "We can't find that page."        
      access_denied:
        title: "403 Forbidden"
        heading: "Access Denied"
        message: "You are not authorized to access this page."
    media_library:
      no_file_provided: "No file provided"
      access_denied: "Access denied"
      index:
        title: "Media Library"
        manage_description: "Manage your uploaded files and images"
        drop_files_here: "Drop files here to upload"
        or_click_to_browse: "or click to browse and select files"
        supports_file_types: "Supports: Images, Videos, Audio, PDFs, Documents"
        uploading_files: "Uploading Files"
        search_placeholder: "Search media files..."
        all_types: "All Types"
        images: "Images"
        videos: "Videos"
        audio: "Audio"
        pdfs: "PDFs"
        no_media_files_yet: "No media files yet"
        upload_first_file: "Upload your first file to get started"
        upload_files: "Upload Files"
        media_details: "Media Details"
        details:
          view_original: "View Original"
          copy_url: "Copy URL"
          update: "Update"
          delete: "Delete"
          alt_text: "Alt Text"
          alt_text_placeholder: "Describe this image for screen readers"
          description_placeholder: "Optional description"
          file_information: "File Information"
          filename: "Filename"
          file_type: "File Type"
          file_size: "File Size"
          dimensions: "Dimensions"
          uploaded: "Uploaded"
          uploaded_by: "Uploaded by"
        item:
          ago: "ago"
          
  # Resource-specific translations
  resources:
    posts:
      index:
        heading: "Posts"
        no_posts: "No posts found"
      dashboard_list:
        heading: "My Posts"
      new:
        heading: "New Post"
      edit:
        heading: "Edit Post"
      show:
        title: "Showing Post"
      list: "List Posts"

    pages:
      index:
        heading: "Pages"
        no_pages: "No pages found"
      dashboard_list:
        heading: "Pages"
      new:
        heading: "New Page"
      edit:
        heading: "Edit Page"
      list: "List Pages"

    categories:
      index:
        heading: "Categories"
        no_categories: "No categories found"
      dashboard_list:
        heading: "Categories"
      new:
        heading: "New Category"
      edit:
        heading: "Edit Category"
      list: "List Categories"

    tags:
      index:
        heading: "Tags"
        no_tags: "No tags found"
      dashboard_list:
        heading: "Tags"
      new:
        heading: "New Tag"
      edit:
        heading: "Edit Tag"
      list: "List Tags"

    users:
      index:
        heading: "Users"
        no_users: "No users found"
      new:
        heading: "New User"
      edit:
        heading: "Edit User"
      list: "List Users"

    menus:
      index:
        heading: "Menus"
        no_menus: "No menus found"
      new:
        heading: "Add Menu Item"
      edit:
        heading: "Edit Menu"
      list: "List Menus"

  # Flash messages
  flash:
    notices:
      created: "%{model} created successfully!"
      updated: "%{model} updated successfully!"
      destroyed: "%{model} was successfully destroyed."      
    errors:
      oops: "Oops, something went wrong."
      user_not_created: "User could not be created."
      user_not_updated: "User could not be updated."      
      post_not_created: "Post could not be created."
      post_not_updated: "Post could not be updated."
      category_not_created: "Category could not be created."
      category_not_updated: "Category could not be updated."
    warnings:
      max_desc: "Maximum 160 characters"
      leave_password_blank: "Leave password fields blank to keep the current password."

  # Authorization and access control
  authorization:
    access_denied:
      message: "You are not authorized to access this page."
      title: "Access Denied"

  # ActiveRecord translations
  activerecord:
    models:
      post: "Post"
      page: "Page"
      category: "Category"
      tag: "Tag"
      user: "User"
      media_item: "Media Item"
    attributes:
      post:
        title: "Title"
        excerpt: "Excerpt"
        body: "Content"
        category: "Category"
        published: "Published"
        published_at: "Published at"
        featured_image: "Featured Image"
        seo_title: "SEO Title"
      category:
        name: "Name"
        slug: "Slug"
        description: "Description"
      tag:
        name: "Name"
        slug: "Slug"
      user:
        email: "Email"
        password: "Password"
        password_confirmation: "Password Confirmation"
        role: "Role"
        role_select: "Select a role"
        avatar: "Avatar"
        new_password: "New Password"
        new_password_placeholder: "New Password (optional)"

  # Date and time formats
  date:
    formats:
      default: "%Y-%m-%d"
      short: "%b %d"
      long: "%B %d, %Y"
    day_names: [Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday]
    abbr_day_names: [Sun, Mon, Tue, Wed, Thu, Fri, Sat]
    month_names: [~, January, February, March, April, May, June, July, August, September, October, November, December]
    abbr_month_names: [~, Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec]

  time:
    formats:
      default: "%a, %d %b %Y %H:%M:%S %z"
      short: "%d %b %H:%M"
      long: "%B %d, %Y %H:%M"