class CreateMediaItems < ActiveRecord::Migration[8.0]
  def change
    create_table :media_items do |t|
      t.string :title
      t.text :alt_text
      t.text :description
      t.bigint :file_size
      t.string :content_type
      t.string :original_filename
      t.references :user, null: false, foreign_key: true
      t.string :storage_key
      t.boolean :is_image
      t.integer :width
      t.integer :height

      t.timestamps
    end

    add_index :media_items, :content_type
    add_index :media_items, :is_image
    add_index :media_items, :created_at
    add_index :media_items, :storage_key, unique: true
  end
end
