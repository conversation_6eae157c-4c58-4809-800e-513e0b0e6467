GEM
  remote: https://rubygems.org/
  specs:
    action_text-trix (2.1.15)
      railties
    actioncable (8.1.1)
      actionpack (= 8.1.1)
      activesupport (= 8.1.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.1.1)
      actionpack (= 8.1.1)
      activejob (= 8.1.1)
      activerecord (= 8.1.1)
      activestorage (= 8.1.1)
      activesupport (= 8.1.1)
      mail (>= 2.8.0)
    actionmailer (8.1.1)
      actionpack (= 8.1.1)
      actionview (= 8.1.1)
      activejob (= 8.1.1)
      activesupport (= 8.1.1)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.1.1)
      actionview (= 8.1.1)
      activesupport (= 8.1.1)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.1.1)
      action_text-trix (~> 2.1.15)
      actionpack (= 8.1.1)
      activerecord (= 8.1.1)
      activestorage (= 8.1.1)
      activesupport (= 8.1.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.1.1)
      activesupport (= 8.1.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.1.1)
      activesupport (= 8.1.1)
      globalid (>= 0.3.6)
    activemodel (8.1.1)
      activesupport (= 8.1.1)
    activerecord (8.1.1)
      activemodel (= 8.1.1)
      activesupport (= 8.1.1)
      timeout (>= 0.4.0)
    activestorage (8.1.1)
      actionpack (= 8.1.1)
      activejob (= 8.1.1)
      activerecord (= 8.1.1)
      activesupport (= 8.1.1)
      marcel (~> 1.0)
    activesupport (8.1.1)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      json
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.3)
    base64 (0.3.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    bigdecimal (3.3.1)
    bindex (0.8.1)
    bootsnap (1.19.0)
      msgpack (~> 1.2)
    brakeman (7.1.1)
      racc
    builder (3.3.0)
    bullet (8.1.0)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    cancancan (3.6.1)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.4)
    crass (1.0.6)
    css-zero (1.1.15)
    cssbundling-rails (1.4.3)
      railties (>= 6.0.0)
    date (3.5.0)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    dotenv (3.1.8)
    drb (2.2.3)
    ed25519 (1.4.0)
    erb (6.0.0)
    erubi (1.13.1)
    et-orbi (1.4.0)
      tzinfo
    faraday (2.14.0)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.2)
      net-http (~> 0.5)
    faraday-retry (2.3.2)
      faraday (~> 2.0)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-aarch64-linux-musl)
    ffi (1.17.2-arm-linux-gnu)
    ffi (1.17.2-arm-linux-musl)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi (1.17.2-x86_64-linux-musl)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    fugit (1.12.1)
      et-orbi (~> 1.4)
      raabro (~> 1.4)
    gapic-common (1.2.0)
      faraday (>= 1.9, < 3.a)
      faraday-retry (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      google-protobuf (~> 4.26)
      googleapis-common-protos (~> 1.6)
      googleapis-common-protos-types (~> 1.15)
      googleauth (~> 1.12)
      grpc (~> 1.66)
    globalid (1.3.0)
      activesupport (>= 6.1)
    google-analytics-data-v1beta (0.19.1)
      gapic-common (~> 1.2)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.3.1)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.5.0)
    google-logging-utils (0.2.0)
    google-protobuf (4.33.1)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.33.1-aarch64-linux-gnu)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.33.1-aarch64-linux-musl)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.33.1-x86_64-linux-gnu)
      bigdecimal
      rake (>= 13)
    google-protobuf (4.33.1-x86_64-linux-musl)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos (1.9.0)
      google-protobuf (~> 4.26)
      googleapis-common-protos-types (~> 1.21)
      grpc (~> 1.41)
    googleapis-common-protos-types (1.22.0)
      google-protobuf (~> 4.26)
    googleauth (1.15.1)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 4.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    groupdate (6.7.0)
      activesupport (>= 7.1)
    grpc (1.76.0)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.76.0-aarch64-linux-gnu)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.76.0-aarch64-linux-musl)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.76.0-x86_64-linux-gnu)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    grpc (1.76.0-x86_64-linux-musl)
      google-protobuf (>= 3.25, < 5.0)
      googleapis-common-protos-types (~> 1.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.8.1)
    irb (1.15.3)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.14.1)
      actionview (>= 7.0.0)
      activesupport (>= 7.0.0)
    jsbundling-rails (1.3.1)
      railties (>= 6.0.0)
    json (2.16.0)
    jwt (3.1.2)
      base64
    kamal (2.8.2)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.4)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    language_server-protocol (********)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.9.0)
      logger
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.1.0)
    matrix (0.4.3)
    meta-tags (2.22.2)
      actionpack (>= 6.0.0, < 8.2)
    mini_magick (5.3.1)
      logger
    mini_mime (1.1.5)
    minitest (5.26.1)
    msgpack (1.8.0)
    multi_json (1.17.0)
    net-http (0.8.0)
      uri (>= 0.11.1)
    net-imap (0.5.12)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.5)
    nokogiri (1.18.10-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.10-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.10-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.10-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.10-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.10-x86_64-linux-musl)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.3)
    pagy (43.0.6)
      json
      yaml
    parallel (1.27.0)
    parser (3.3.10.0)
      ast (~> 2.4.1)
      racc
    pg (1.6.2)
    pg (1.6.2-aarch64-linux)
    pg (1.6.2-aarch64-linux-musl)
    pg (1.6.2-x86_64-linux)
    pg (1.6.2-x86_64-linux-musl)
    pp (0.6.3)
      prettyprint
    prettyprint (0.2.0)
    prism (1.6.0)
    propshaft (1.3.1)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (7.1.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.2.4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.1.1)
      actioncable (= 8.1.1)
      actionmailbox (= 8.1.1)
      actionmailer (= 8.1.1)
      actionpack (= 8.1.1)
      actiontext (= 8.1.1)
      actionview (= 8.1.1)
      activejob (= 8.1.1)
      activemodel (= 8.1.1)
      activerecord (= 8.1.1)
      activestorage (= 8.1.1)
      activesupport (= 8.1.1)
      bundler (>= 1.15.0)
      railties (= 8.1.1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails_pulse (0.2.4)
      css-zero (~> 1.1, >= 1.1.4)
      groupdate (~> 6.0)
      pagy (>= 8, < 44)
      rails (>= 7.1.0, < 9.0.0)
      ransack (~> 4.0)
      request_store (~> 1.5)
      turbo-rails (~> 2.0.11)
    railties (8.1.1)
      actionpack (= 8.1.1)
      activesupport (= 8.1.1)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      tsort (>= 0.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.1)
    ransack (4.4.1)
      activerecord (>= 7.2)
      activesupport (>= 7.2)
      i18n
    rdoc (6.15.1)
      erb
      psych (>= 4.0.0)
      tsort
    regexp_parser (2.11.3)
    reline (0.6.3)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.2.0)
      actionpack (>= 7.0)
      railties (>= 7.0)
    rexml (3.4.4)
    rubocop (1.81.7)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.47.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.48.0)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-performance (1.26.1)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.47.1, < 2.0)
    rubocop-rails (2.34.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.5)
      ffi (~> 1.12)
      logger
    rubyzip (3.2.2)
    securerandom (0.4.1)
    selenium-webdriver (4.38.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 4.0)
      websocket (~> 1.0)
    signet (0.21.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 4.0)
      multi_json (~> 1.10)
    sitemap_generator (6.3.0)
      builder (~> 3.0)
    solid_cable (3.0.12)
      actioncable (>= 7.2)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_cache (1.0.10)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_queue (1.2.4)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11)
      railties (>= 7.1)
      thor (>= 1.3.1)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.8)
    tailwindcss-ruby (4.1.16)
    tailwindcss-ruby (4.1.16-aarch64-linux-gnu)
    tailwindcss-ruby (4.1.16-aarch64-linux-musl)
    tailwindcss-ruby (4.1.16-x86_64-linux-gnu)
    tailwindcss-ruby (4.1.16-x86_64-linux-musl)
    thor (1.4.0)
    thruster (0.1.16)
    thruster (0.1.16-aarch64-linux)
    thruster (0.1.16-x86_64-linux)
    timeout (0.4.4)
    tsort (0.2.0)
    turbo-rails (2.0.20)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.2.0)
      unicode-emoji (~> 4.1)
    unicode-emoji (4.1.0)
    uniform_notifier (1.18.0)
    uri (1.1.1)
    useragent (0.16.11)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yaml (0.4.0)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  bootsnap
  brakeman
  bullet
  cancancan (~> 3.6)
  capybara
  cssbundling-rails (~> 1.4)
  debug
  devise (~> 4.9)
  friendly_id (~> 5.5)
  google-analytics-data-v1beta
  image_processing (~> 1.2)
  jbuilder
  jsbundling-rails (~> 1.3)
  kamal
  meta-tags (~> 2.22)
  pagy
  pg (~> 1.1)
  propshaft
  puma (>= 5.0)
  rails (~> 8.1.1)
  rails_pulse (~> 0.2.3)
  rubocop-rails-omakase
  selenium-webdriver
  sitemap_generator (~> 6.3)
  solid_cable
  solid_cache
  solid_queue
  stimulus-rails
  tailwindcss-ruby
  thruster
  turbo-rails
  tzinfo-data
  web-console

BUNDLED WITH
   2.6.9
