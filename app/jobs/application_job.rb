class ApplicationJob < ActiveJob::Base
  # Automatically retry jobs that encountered a deadlock
  # retry_on ActiveRecord::Deadlocked

  # Most jobs are safe to ignore if the underlying records are no longer available
  # discard_on ActiveJob::DeserializationError
  # # Schedule to run 5 minutes past every hour. cron: 5 * * * *
  RailsPulse::SummaryJob.perform_later

  # Schedule to run daily. cron: 0 1 * * *
  RailsPulse::CleanupJob.perform_later
end
