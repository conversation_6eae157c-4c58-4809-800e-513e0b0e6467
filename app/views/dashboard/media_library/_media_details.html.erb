<% cache media_item do %>
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
  <div class="space-y-4">
    <div class="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
      <% if media_item.image? %>
        <img 
          src="<%= media_item.file_url %>" 
          alt="<%= media_item.alt_text || media_item.title %>"
          class="w-full h-auto max-h-96 object-contain">
      <% else %>
        <div class="h-48 flex items-center justify-center">
          <% case media_item.content_type %>
          <% when /^video\// %>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round" d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z" />
            </svg>
          <% when /^audio\// %>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round" d="m9 9 10.5-3m0 6.553v3.75a2.25 2.25 0 0 1-1.632 2.163l-1.32.377a1.803 1.803 0 1 1-.99-3.467l2.31-.66a2.25 2.25 0 0 0 1.632-2.163Zm0 0V2.25L9 5.25v10.303m0 0v3.75a2.25 2.25 0 0 1-1.632 2.163l-1.32.377a1.803 1.803 0 0 1-.99-3.467l2.31-.66A2.25 2.25 0 0 0 9 15.553Z" />
            </svg>
          <% when "application/pdf" %>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 text-red-500">
              <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
            </svg>
          <% else %>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 18.75h-9A1.5 1.5 0 0 1 2.25 18v-1.5m18-9v-1.5a1.5 1.5 0 0 0-1.5-1.5h-1.5m1.5 9a1.5 1.5 0 0 1-1.5 1.5H14.25m4.5-9H18a1.5 1.5 0 0 0-1.5-1.5H9a1.5 1.5 0 0 0-1.5 1.5v1.5m4.5-9v9" />
            </svg>
          <% end %>
        </div>
      <% end %>
    </div>
    
    <!-- Quick Actions -->
    <div class="flex gap-2">
      <a 
        href="<%= media_item.file_url %>" 
        target="_blank"
        class="btn btn-outline btn-sm flex-1">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
        </svg>
        <%= translate_page_element(:media_library, "details.view_original") %>
      </a>
      
      <button 
        class="btn btn-outline btn-sm"
        data-action="click->media-library#copyUrl"
        data-url="<%= media_item.file_url %>">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
        </svg>
        <%= translate_page_element(:media_library, "details.copy_url") %>
      </button>
    </div>
  </div>
  
  <!-- Media Information and Edit Form -->
  <div class="space-y-6">
    <%= form_with model: [:dashboard, media_item], local: false, data: { action: "submit->media-library#updateMedia" } do |form| %>
      <div class="space-y-4">
        <div>
          <%= form.label :title, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
          <%= form.text_field :title, class: "input input-bordered w-full" %>
        </div>
        
        <div>
          <%= form.label :alt_text, t(".details.alt_text"), class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
          <%= form.text_area :alt_text, rows: 2, class: "textarea textarea-bordered w-full", placeholder: t(".details.alt_text_placeholder") %>
        </div>
        
        <div>
          <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
          <%= form.text_area :description, rows: 3, class: "textarea textarea-bordered w-full", placeholder: t(".details.description_placeholder") %>
        </div>
        
        <div class="flex gap-2">
          <%= form.submit t(".details.update"), class: "btn btn-primary" %>
          <% if can? :destroy, media_item %>
            <button 
              type="button"
              class="btn btn-error"
              data-action="click->media-library#deleteMedia"
              data-media-id="<%= media_item.id %>">
              <%= t(".details.delete") %>
            </button>
          <% end %>
        </div>
      </div>
    <% end %>
    
    <!-- File Information -->
    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
      <h4 class="font-medium text-gray-900 dark:text-white mb-3"><%= t(".details.file_information") %></h4>
      
      <dl class="space-y-2 text-sm">
        <div class="flex justify-between">
          <dt class="text-gray-600 dark:text-gray-400"><%= t(".details.filename") %></dt>
          <dd class="text-gray-900 dark:text-white font-mono"><%= media_item.original_filename %></dd>
        </div>
        
        <div class="flex justify-between">
          <dt class="text-gray-600 dark:text-gray-400"><%= t(".details.file_type") %></dt>
          <dd class="text-gray-900 dark:text-white"><%= media_item.content_type %></dd>
        </div>
        
        <div class="flex justify-between">
          <dt class="text-gray-600 dark:text-gray-400"><%= t(".details.file_size") %></dt>
          <dd class="text-gray-900 dark:text-white"><%= media_item.file_size_human %></dd>
        </div>
        
        <% if media_item.dimensions %>
          <div class="flex justify-between">
            <dt class="text-gray-600 dark:text-gray-400"><%= t(".details.dimensions") %></dt>
            <dd class="text-gray-900 dark:text-white"><%= media_item.dimensions %></dd>
          </div>
        <% end %>
        
        <div class="flex justify-between">
          <dt class="text-gray-600 dark:text-gray-400"><%= t(".details.uploaded") %></dt>
          <dd class="text-gray-900 dark:text-white"><%= media_item.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
        </div>
        
        <div class="flex justify-between">
          <dt class="text-gray-600 dark:text-gray-400"><%= t(".details.uploaded_by") %></dt>
          <dd class="text-gray-900 dark:text-white"><%= media_item.user.email %></dd>
        </div>
      </dl>
    </div>
  </div>
</div>
<% end %>
