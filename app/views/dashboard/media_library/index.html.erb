<% content_for :title, translate_page_element(:media_library, :title) %>

<div class="card shadow-xl bg-base-100 mx-auto px-4 py-6" data-controller="media-library">
  <!-- Header -->
  <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
    <div>
      <h1 class="text-2xl font-bold mb-4"><%= translate_page_element(:media_library, :title) %></h1>
      <p class="text-neutral-content"><%= translate_page_element(:media_library, :manage_description) %></p>
    </div>
  </div>

  <!-- Test Button -->
  <div class="mb-4">
    <button
      type="button"
      class="btn btn-primary"
      data-action="click->media-library#testMethod">
      Test Media Library Controller
    </button>
    <button
      type="button"
      class="btn btn-secondary ml-2"
      onclick="alert('JavaScript is working!')">
      Test JavaScript
    </button>
  </div>

  <!-- Simple Test Controller -->
  <div class="mb-4" data-controller="test">
    <button
      type="button"
      class="btn btn-accent"
      data-action="click->test#test">
      Test Simple Controller
    </button>
  </div>

  <!-- Drag and Drop Upload Area -->
  <div class="mb-6">
    <div
      class="border-2 border-dashed rounded-lg p-8 text-center hover:border-primary transition-colors text-neutral-content"
      data-media-library-target="dropZone"
      data-action="dragover->media-library#handleDragOver dragenter->media-library#handleDragEnter dragleave->media-library#handleDragLeave drop->media-library#handleDrop click->media-library#openFileDialog">

      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 mx-auto text-gray-400 mb-4">
        <path stroke-linecap="round" stroke-linejoin="round" d="M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18.75 19.5H6.75Z" />
      </svg>

      <h3 class="text-xl font-semibold mb-2">
        <%= translate_page_element(:media_library, :drop_files_here) %>
      </h3>
      <p class="mb-4">
        <%= translate_page_element(:media_library, :or_click_to_browse) %>
      </p>
      <p class="text-sm">
        <%= translate_page_element(:media_library, :supports_file_types) %>
      </p>

      <input
        type="file"
        multiple
        accept="image/*,video/*,audio/*,.pdf"
        class="hidden"
        data-media-library-target="fileInput"
        data-action="change->media-library#handleFileSelect">
    </div>

    <!-- Upload Progress -->
    <div
      class="mt-4 hidden"
      data-media-library-target="uploadProgress">
      <div class="border-x-2 border-t-2 border-base-300 border-dashed rounded-lg">
        <h4 class="font-medium p-2"><%= translate_page_element(:media_library, :uploading_files) %></h4>
        <div class="space-y-2" data-media-library-target="uploadList">
          <!-- Upload items will be inserted here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="rounded-lg shadow-sm border-2 border-base-300 border-dashed p-4 mb-6">
    <div class="flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <input 
          type="text" 
          placeholder="<%= translate_page_element(:media_library, :search_placeholder) %>" 
          class="input input-bordered w-full"
          data-media-library-target="searchInput"
          data-action="input->media-library#search">
      </div>
      
      <div class="flex gap-2">
        <select 
          class="select select-bordered"
          data-media-library-target="typeFilter"
          data-action="change->media-library#filter">
          <option value=""><%= translate_page_element(:media_library, :all_types) %></option>
          <option value="image/"><%= translate_page_element(:media_library, :images) %></option>
          <option value="video/"><%= translate_page_element(:media_library, :videos) %></option>
          <option value="audio/"><%= translate_page_element(:media_library, :audio) %></option>
          <option value="application/pdf"><%= translate_page_element(:media_library, :pdfs) %></option>
        </select>
        
        <button 
          class="btn btn-outline border-dashed hover:btn-primary"
          data-action="click->media-library#toggleView"
          data-media-library-target="viewToggle">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <div id="content-wrapper" data-media-library-target="contentWrapper">
    <!-- Media Grid -->
    <div
      class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"
      data-media-library-target="mediaGrid">
      <% @media_items.each do |media_item| %>
        <%= render 'media_item', media_item: media_item %>
      <% end %>
    </div>

    <!-- Empty State -->
    <% if @media_items.empty? %>
      <div class="text-center py-12" data-media-library-target="emptyState">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 mx-auto text-gray-400 mb-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2"><%= translate_page_element(:media_library, :no_media_files_yet) %></h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4"><%= translate_page_element(:media_library, :upload_first_file) %></p>
        <button
          class="btn btn-primary"
          data-action="click->media-library#scrollToUploadArea">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18.75 19.5H6.75Z" />
          </svg>
          <%= translate_page_element(:media_library, :upload_files) %>
        </button>
      </div>
    <% end %>

    <!-- Pagination -->
    <%= render 'partials/pagination', pagy: @pagy %>
  </div>
</div>

<!-- Confirmation Modal -->
<div data-controller="confirm">
  <%= render 'partials/dashboard/confirm' %>
</div>

<!-- Media Viewer Modal -->
<dialog data-media-library-target="viewerModal" class="modal">
  <div class="modal-box w-11/12 max-w-5xl p-0 bg-base-200">
    <div data-media-library-target="viewerContent">
      <!-- Media content will be loaded here by Stimulus -->
    </div>
  </div>
  <form method="dialog" class="modal-backdrop">
    <!-- The :stop modifier prevents the modal from closing when the backdrop is clicked, which is good for videos -->
    <button data-action="click->media-library#closeViewerModal">close</button>
  </form>
</dialog>
