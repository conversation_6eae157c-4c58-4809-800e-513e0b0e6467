<% cache media_item do %><div
  id="<%= dom_id(media_item) %>"
  class="bg-base-200 rounded-lg shadow-sm border-2 border-base-300 border-dashed overflow-hidden hover:shadow-xl transition-shadow cursor-pointer group"
  data-media-item-id="<%= media_item.id %>">
    
  <div class="aspect-square relative overflow-hidden">
    <% if media_item.image? %>
      <div class="skeleton w-full h-full"></div>
      <img 
        src="<%= media_item.thumbnail_url %>" 
        alt="<%= media_item.alt_text || media_item.title %>"
        class="w-full h-full object-cover absolute top-0 left-0 opacity-0 transition-opacity duration-500"
        loading="lazy"
        onload="this.classList.remove('opacity-0')">
    <% else %>
      <div class="w-full h-full flex items-center justify-center">
        <% case media_item.content_type %>
        <% when /^video\// %>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-12 h-12 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z" />
          </svg>
        <% when /^audio\// %>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-12 h-12 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="m9 9 10.5-3m0 6.553v3.75a2.25 2.25 0 0 1-1.632 2.163l-1.32.377a1.803 1.803 0 1 1-.99-3.467l2.31-.66a2.25 2.25 0 0 0 1.632-2.163Zm0 0V2.25L9 5.25v10.303m0 0v3.75a2.25 2.25 0 0 1-1.632 2.163l-1.32.377a1.803 1.803 0 0 1-.99-3.467l2.31-.66A2.25 2.25 0 0 0 9 15.553Z" />
          </svg>
        <% when "application/pdf" %>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-12 h-12 text-red-500">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
          </svg>
        <% else %>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-12 h-12 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 18.75h-9A1.5 1.5 0 0 1 2.25 18v-1.5m18-9v-1.5a1.5 1.5 0 0 0-1.5-1.5h-1.5m1.5 9a1.5 1.5 0 0 1-1.5 1.5H14.25m4.5-9H18a1.5 1.5 0 0 0-1.5-1.5H9a1.5 1.5 0 0 0-1.5 1.5v1.5m4.5-9v9" />
          </svg>
        <% end %>
      </div>
    <% end %>
        
    <div class="absolute inset-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
      <div class="flex gap-2">
        <button 
          type="button"
          class="btn btn-sm btn-circle bg-white text-gray-800 hover:bg-gray-100"
          data-action="click->media-library#showMediaViewer"
          data-media-url="<%= url_for(media_item.file) %>"
          data-media-type="<%= media_item.content_type %>"
          data-media-title="<%= media_item.title %>"
          title="<%= translate_action(:show) %>">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
          </svg>
        </button>
        
        <% if can? :destroy, media_item %>
          <button
            class="btn btn-sm btn-circle bg-red-500 text-white hover:bg-red-600"
            data-action="click->confirm#open:stop"
            data-url="<%= dashboard_media_library_path(media_item) %>"
            data-method="delete"
            data-name="<%= media_item.title %>"
            title="<%= t('common.actions.delete') %>">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
              <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244 2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
            </svg>
          </button>
        <% end %>
      </div>
    </div>
  </div>
    
  <div class="p-3">
    <h3 class="font-medium text-sm truncate" title="<%= media_item.title %>">
      <%= media_item.title %>
    </h3>    

    <div class="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
      <span><%= media_item.file_size_human %></span>
      <% if media_item.dimensions %>
        <span><%= media_item.dimensions %></span>
      <% end %>
      <span class="badge badge-dash badge-primary badge-sm"><%= media_item.content_type.split('/').last.upcase %></span>
    </div>
    
    <div class="mt-1 text-xs text-gray-400 dark:text-gray-500">
      <%= time_ago_in_words(media_item.created_at) %> <%= translate_page_element(:media_library, "item.ago") %>
    </div>
  </div>
</div><% end %>
