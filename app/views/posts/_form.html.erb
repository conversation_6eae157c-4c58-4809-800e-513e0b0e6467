<%= form_with(model: post, url: post.persisted? ? dashboard_post_path(post) : dashboard_posts_path, class: "contents") do |form| %>
<%= render "partials/dashboard/form_error", resource: post %>
<!-- Featured Image Section -->
<%= render 'partials/dashboard/media_picker',
           form: form,
           field_name: :featured_media_item_id,
           label: "Featured Image",
           current_media_item: post.featured_media_item,
           current_attachment: post.featured_image,
           modal_id: "featured-image-selector",
           preview_class: "w-full h-auto rounded-md",
           empty_state_text: "No featured image selected",
           description: "Select or upload a featured image for this post. This image will be displayed in post previews and social media shares." %>
<!-- End Featured Image Section -->
<div class="grid grid-cols-1 gap-4 lg:grid-cols-3 lg:gap-8">
  <div class=" lg:col-span-2">
    <div class="my-5" data-controller="character-counter" data-character-counter-max-length-value="70">
      <div class="flex justify-between">
        <%= form.label :title %>
        <span class="text-sm" data-character-counter-target="output"></span>
      </div>
      <%= form.text_field :title, class: ["input w-full", {"input-neutral": post.errors[:title].none?, "input-error": post.errors[:title].any?}], data: { action: "input->character-counter#count", "character-counter-target": "input" } %>
    </div>
    <div class="my-5">
      <%= form.label :body %>
      <div
        data-controller="tiptap"
        data-tiptap-content-value="<%= @post.body.to_json %>"
      >
        <%= form.hidden_field :body, data: { "tiptap-target": "input" } %>
        <div data-tiptap-target="editor" class="border rounded-md" style="min-height: 250px;"></div>

        <div class="mt-2 flex gap-2">
          <button type="button" class="btn" data-action="click->tiptap#insertImageByUrl">Insert URL image</button>

          <label class="btn">
            Upload image
            <input type="file" accept="image/*" data-action="change->tiptap#uploadFile" data-tiptap-target="fileInput" style="display:none" />
          </label>        
          <button type="button" data-align="left" data-action="click->tiptap#setAlign">Left</button>
          <button type="button" data-align="center" data-action="click->tiptap#setAlign">Center</button>
          <button type="button" data-align="right" data-action="click->tiptap#setAlign">Right</button>
          <button type="button" data-align="justify" data-action="click->tiptap#setAlign">Justify</button>
        </div>
      </div>
    </div>
  </div>
  <div id="right-side">
    <div class="my-5">
      <%= form.label :slug %>
      <%= form.text_field :slug, class: ["input w-full", {"input-neutral": post.errors[:slug].none?, "input-error": post.errors[:slug].any?}] %>
      <p class="text-sm mt-1">The slug is auto-generated from the title if left blank.</p>
    </div>
    <div class="my-5">
      <%= form.label :category %>
      <%= form.collection_select :category_id, Category.all, :id, :name, { prompt: "Select a category" }, { class: ["select w-full", {"select-neutral": post.errors[:category_id].none?, "select-error": post.errors[:category_id].any?}] } %>
    </div>
    <div class="my-5" data-controller="character-counter" data-character-counter-max-length-value="160">
      <div class="flex justify-between">
        <%= form.label :excerpt %>
        <span class="text-sm" data-character-counter-target="output"></span>
      </div>
      <%= form.textarea :excerpt, rows: 4, class: ["textarea w-full", {"textarea-neutral": post.errors[:excerpt].none?, "textarea-error": post.errors[:excerpt].any?}], data: { action: "input->character-counter#count", "character-counter-target": "input" } %>
      <p class="text-sm mt-1">A short summary of the post. Max 160 characters.</p>
    </div>
  </div>
</div>  

  <div class="my-5" data-controller="character-counter" data-character-counter-max-length-value="70">
    <div class="flex justify-between">
      <%= form.label :seo_title %>
      <span class="text-sm" data-character-counter-target="output"></span>
    </div>
    <%= form.text_field :seo_title, class: ["input w-full", {"input-neutral": post.errors[:seo_title].none?, "input-error": post.errors[:seo_title].any?}], data: { action: "input->character-counter#count", "character-counter-target": "input" } %>
    <p class="text-sm mt-1">If blank, the main title will be used. Max 70 characters.</p>
  </div>

  <div class="my-5">
    <%= form.label :meta_description %>
    <%= form.text_field :meta_description, class: ["input w-full", {"input-neutral": post.errors[:meta_description].none?, "input-error": post.errors[:meta_description].any?}] %>
    <p class="text-sm mt-1">If blank, the excerpt will be used.</p>
  </div>

  <div class="my-5 flex items-center gap-2">
    <%= form.label :published %>
    <%= form.checkbox :published, class: ["checkbox", {"checkbox-neutral": post.errors[:published].none?, "checkbox-error": post.errors[:published].any?}] %>
  </div>

  <div class="my-5">
    <%= form.label :published_at %>
    <%= form.datetime_field :published_at, class: ["input w-full", {"input-neutral": post.errors[:published_at].none?, "input-error": post.errors[:published_at].any?}] %>
  </div>

  <div class="inline">
    <%= form.submit nil, class: "btn btn-outline btn-dash btn-primary" %>
    <%= link_to translate_action(:back), dashboard_posts_path, class: "btn btn-outline btn-dash btn-secondary" %>
  </div>
<% end %>


