<%= turbo_frame_tag dom_id(tag, :form) do %>
<%= form_with(model: [ :dashboard, tag ]) do |form| %>
  <%= render "partials/dashboard/form_error", resource: tag %>

  <div class="form-control">
    <%= form.label :name, translate_field(:name), class: "label" %>
    <%= form.text_field :name, class: "input input-bordered w-full" %>
  </div>

  <div class="modal-action">
    <%= form.submit translate_action(:save), class: "btn btn-primary" %>
  </div>
<% end %>
<% end %>
