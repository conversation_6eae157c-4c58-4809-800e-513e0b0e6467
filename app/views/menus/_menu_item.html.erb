<li data-id="<%= menu_item.id %>" class="rounded-box">
  <div class="flex justify-between items-center w-full">
    <span class="flex-grow flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 cursor-grab handle" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
      <%= menu_item.name %>
    </span>
    <div class="flex-none">      
      <button type="button" class="btn btn-xs btn-error" data-action="click->confirm#open" data-url="<%= dashboard_menu_menu_item_path(menu_item.menu, menu_item) %>" data-method="delete" data-name="<%= menu_item.name %>" >
        <%= translate_action(:delete) %>
      </button>
    </div>
  </div>
  <ul class="nested-menu-list">
    <% children_data = local_assigns.fetch(:children, []) %>
    <% children_data.each do |child_item, grandchildren| %>
      <%= render "menus/menu_item", menu_item: child_item, children: grandchildren %>
    <% end %>
  </ul>
</li>
