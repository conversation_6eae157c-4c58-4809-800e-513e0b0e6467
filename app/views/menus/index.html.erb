<div class="p-4">
  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold card-title"><%= translate_resource_heading(:menu, :index) %></h1>
        <label for="new_menu_modal" class="btn btn-dash btn-primary"><%= translate_action(:add, Menu) %></label>        
      </div>

      <div class="overflow-x-auto">
        <table class="table w-full">
          <thead>
            <tr>
              <th><%= translate_field(:name) %></th>
              <th class="text-right"><%= translate_field(:actions) %></th>
            </tr>
          </thead>
          <tbody id="menus">
            <% @menus.each do |menu| %>
              <tr id="<%= dom_id(menu) %>">
                <td><%= menu.name %></td>
                <td class="text-right">
                  <div class="join">
                    <%= link_to translate_action(:show), dashboard_menu_path(menu), class: "btn btn-dash btn-primary btn-sm join-item" %>                    
                    <button type="button" class="btn btn-dash btn-secondary btn-sm join-item" data-action="click->modal-remote#open" data-url="<%= edit_dashboard_menu_path(menu, modal: 1) %>">
                        <%= translate_action(:edit) %>
                    </button>                    
                    <button type="button" class="btn btn-dash btn-error btn-sm join-item" data-action="click->confirm#open" data-url="<%= dashboard_menu_path(menu) %>" data-method="delete" data-name="<%= menu.name %>">
                        <%= translate_action(:delete) %>
                    </button>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
<%= render "partials/dashboard/new_modal",
    modal_id: "new_menu_modal",
    resource_name: "menu",
    resource_instance: @menu %>