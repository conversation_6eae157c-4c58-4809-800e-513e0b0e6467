<div class="card-body" data-controller="menu-item-form">
  <%= form_with(model: [ :dashboard, menu, menu_item ], html: { class: "space-y-4" }) do |form| %>
    <%= render "partials/dashboard/form_error", resource: menu_item %>
    <div class="form-control">
      <%= form.label :name, translate_field(:name), class: "label" %>
      <%= form.text_field :name, class: "input input-bordered w-full" %>
    </div>

    <div class="form-control">
      <%= form.label :linkable_type, translate_field(:linkable_type), class: "label" %>
      <%= form.select :linkable_type, ['Category', 'Tag', 'Custom URL'], { include_blank: true }, {
        data: {
          "menu-item-form-target": "linkableType",
          action: "change->menu-item-form#toggleFields"
        },
        class: "select select-bordered w-full"
      } %>
    </div>

    <div class="form-control" data-menu-item-form-target="categorySelect" style="display: none;">
      <%= form.label :linkable_id, translate_field(:category), class: "label" %>
      <%= form.collection_select :category_id, Category.all, :id, :name, { include_blank: true }, { name: "menu_item[category_id]", class: "select select-bordered w-full" } %>
    </div>

    <div class="form-control" data-menu-item-form-target="tagSelect" style="display: none;">
      <%= form.label :linkable_id, translate_field(:tag), class: "label" %>
      <%= form.collection_select :tag_id, Tag.all, :id, :name, { include_blank: true }, { name: "menu_item[tag_id]", class: "select select-bordered w-full" } %>
    </div>

    <div class="form-control" data-menu-item-form-target="customUrlField" style="display: none;">
      <%= form.label :url, class: "label" %>
      <%= form.text_field :url, class: "input input-bordered w-full" %>
    </div>

    <div class="actions">
      <%= form.submit translate_action(:add), class: "btn btn-dash btn-primary" %>
    </div>
  <% end %>
</div>