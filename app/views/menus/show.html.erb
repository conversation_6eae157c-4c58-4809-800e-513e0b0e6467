<div class="p-4">
  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <h1 class="text-2xl font-bold card-title mb-4"><%= @menu.name %></h1>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body">
            <h2 class="text-xl font-semibold mb-4 card-title"><%= translate_resource_heading(:menu, :index) %></h2>
            <ul id="menu-items" class="menu bg-base-100 w-full rounded-box" data-reorder-url="<%= reorder_dashboard_menu_menu_items_path(@menu) %>" data-nested="true">
              <% @menu.build_menu_tree.each do |menu_item, children_data| %>
                <%= render "menus/menu_item", menu_item: menu_item, children: children_data %>
              <% end %>
            </ul>
            <p class="text-sm text-primary">* <%= translate_page_element(:menus, :reorder_instruction, section: :show) %></p>
            <p class="text-sm text-primary">* <%= translate_page_element(:menus, :parent_menu_recommendation, section: :show) %></p>
          </div>
        </div>

        <div>
          <h2 class="text-xl font-semibold mb-4"><%= translate_resource_heading(:menu, :new) %></h2>
          <div class="card bg-base-200 shadow-xl">
            <%= turbo_frame_tag dom_id(@menu_item, :form) do %>
              <%= render "menus/form_content", menu: @menu, menu_item: @menu_item %>
            <% end %>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <%= link_to translate_action(:back), dashboard_menus_path, class: "btn" %>
      </div>
    </div>
  </div>
</div>

<style>
  .nested-menu-list:empty {
    display: none;
  }
</style>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
  document.addEventListener('turbo:load', function() {
    const mainList = document.getElementById('menu-items');
    if (mainList && mainList.dataset.nested) {
      const lists = [mainList, ...mainList.querySelectorAll('ul')];
      lists.forEach(list => {
        new Sortable(list, {
          group: 'nested',
          animation: 150,
          fallbackOnBody: true,
          swapThreshold: 0.65,
          handle: '.handle',
          onEnd: () => sendOrder()
        });
      });

      const sendOrder = () => {
        const url = mainList.dataset.reorderUrl;
        const nestedData = serialize(mainList);

        fetch(url, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector("[name='csrf-token']").content
          },
          body: JSON.stringify({ menu_items: nestedData })
        });
      };

      const serialize = (sortable) => {
        const serialized = [];
        [].slice.call(sortable.children).forEach((li) => {
          const item = { id: li.dataset.id };
          const nested = li.querySelector('ul');
          if (nested) {
            item.children = serialize(nested);
          }
          serialized.push(item);
        });
        return serialized;
      };
    }
  });
</script>