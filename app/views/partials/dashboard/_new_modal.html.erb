<%#
  Reusable modal for creating a new resource.

  Locals:
  - modal_id: (String) The unique ID for the modal elements.
    (e.g., "new_tag_modal")
  - resource_name: (String) The singular name of the resource.
    (e.g., "tag")
  - resource_instance: (Object) The new, unsaved instance of the resource.
    (e.g., @tag)
  - form_partial: (String, optional) The path to the form partial.
    Defaults to "form", which renders the _form.html.erb in the
    resource's view directory (e.g., app/views/tags/_form.html.erb).
%>
<input type="checkbox" id="<%= modal_id %>" class="modal-toggle" />
<div class="modal" role="dialog">
  <div class="modal-box">
    <h3 class="font-bold text-lg"><%= translate_resource_heading(resource_name, :new) %></h3>
    <div class="py-4">
        <%= render partial: local_assigns.fetch(:form_partial, "form"), locals: { resource_name.to_sym => resource_instance } %>
    </div>
  </div>
  <label class="modal-backdrop" for="<%= modal_id %>">Close</label>
</div>
