<%# 
  Reusable Media Picker Component
  
  Required locals:
  - form: the form builder object
  - field_name: the name of the field (e.g., :featured_media_item_id, :avatar_media_item_id)
  - label: the label text (e.g., "Featured Image", "Profile Picture")
  - current_media_item: the currently selected media item (optional)
  - current_attachment: the current Active Storage attachment (optional, fallback)
  - modal_id: unique ID for the modal (e.g., "featured-image-selector", "avatar-selector")
  - preview_class: CSS classes for the preview image (optional, defaults to standard)
  - empty_state_text: text to show when no image is selected (optional)
  - description: help text below the picker (optional)
%>

<%
  # Set defaults
  preview_class ||= "w-full h-auto rounded-md"
  empty_state_text ||= "No image selected"
  description ||= nil
  
  # Determine current image URL
  current_url = nil
  if current_media_item&.image?
    current_url = current_media_item.thumbnail_url
  elsif current_attachment&.attached?
    begin
      current_url = rails_representation_url(current_attachment.variant(resize_to_fill: [300, 200]).processed, only_path: true)
    rescue
      current_url = rails_blob_url(current_attachment, only_path: true)
    end
  end
  
  # Use the existing featured image picker controller
  controller_name = "featured-image-picker"
%>

<div class="my-5"
     data-controller="<%= controller_name %>"
     data-<%= controller_name %>-modal-id-value="<%= modal_id %>"
     data-<%= controller_name %>-input-name-value="<%= form.object_name %>[<%= field_name %>]">

  <%= form.label field_name, label, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>

  <!-- Hidden input for the selected media item ID -->
  <%= form.hidden_field field_name, data: { "#{controller_name}-target": "input" } %>

  <!-- Preview Area -->
  <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center">
    <div data-<%= controller_name %>-target="preview">
      <% if current_url %>
        <div class="w-full max-w-sm mx-auto">
          <img src="<%= current_url %>" alt="<%= label %> preview" class="<%= preview_class %>" />
        </div>
      <% else %>
        <div class="text-gray-500 dark:text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 mx-auto mb-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
          </svg>
          <p class="text-sm"><%= empty_state_text %></p>
        </div>
      <% end %>
    </div>

    <!-- Action Buttons -->
    <div class="mt-4 flex gap-2 justify-center">
      <button type="button"
              class="btn btn-primary btn-sm"
              data-action="click-><%= controller_name %>#openModal">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
          <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
        </svg>
        Select Image
      </button>

      <button type="button"
              class="btn btn-outline btn-sm"
              data-action="click-><%= controller_name %>#removeImage"
              data-<%= controller_name %>-target="removeButton"
              style="<%= (current_media_item || (current_attachment&.attached?)) ? '' : 'display: none;' %>">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
          <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
        </svg>
        Remove
      </button>
    </div>
  </div>

  <% if description %>
    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
      <%= description %>
    </p>
  <% end %>
</div>

<!-- Include the media selector modal -->
<%= render 'partials/dashboard/media_selector_modal',
           modal_id: modal_id,
           target_input: "#{form.object_name}[#{field_name}]" %>
