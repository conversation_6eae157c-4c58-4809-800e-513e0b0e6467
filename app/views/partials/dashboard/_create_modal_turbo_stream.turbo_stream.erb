<%# 
  Reusable partial for create modal turbo stream responses
  
  Required locals:
  - resource: the created resource instance (e.g., @tag, @category)
  - resource_class: the resource class (e.g., Tag, Category)
  - resource_name: the resource name as string (e.g., "tag", "category")
  - list_id: the ID of the tbody/container to prepend to (e.g., "tags", "categories")
  - modal_id: the ID of the modal to close (e.g., "new_tag_modal", "new_category_modal")
  - edit_path_helper: symbol for the edit path helper (e.g., :edit_dashboard_tag_path, :edit_dashboard_category_path)
  - show_path_helper: symbol for the show path helper (e.g., :tag_path, :category_path)
  - delete_path_helper: symbol for the delete path helper (e.g., :dashboard_tag_path, :dashboard_category_path)
  - additional_columns: array of additional column data to display (optional)
%>

<%# Prepend the new resource to the list %>
<%= turbo_stream.prepend list_id do %>
    <tr id="<%= dom_id(resource) %>">
        <td><%= resource.name %></td>
        <% if additional_columns.present? %>
          <% additional_columns.each do |column| %>
            <td><%= resource.send(column) %></td>
          <% end %>
        <% end %>
        <td class="text-right">
            <div class="join">
                <%= link_to translate_action(:show), send(show_path_helper, resource), class: "btn btn-dash btn-primary btn-sm join-item", target: "_blank" %>
                <button type="button" class="btn btn-dash btn-secondary btn-sm join-item" data-action="click->modal-remote#open" data-url="<%= send(edit_path_helper, resource, modal: 1) %>"><%= translate_action(:edit) %></button>
                <button type="button" class="btn btn-dash btn-error btn-sm join-item" data-action="click->confirm#open" data-url="<%= send(delete_path_helper, resource) %>" data-method="delete" data-name="<%= resource.name %>">
                    <%= translate_action(:delete) %>
                </button>
            </div>
        </td>
    </tr>
<% end %>

<%# Reset the form and close the modal %>
<%= turbo_stream.update dom_id(resource_class.new, :form), partial: "form", locals: { resource_name.to_sym => resource_class.new } %>

<%# Close the modal and inject the rendered alerts partial into the page %>
<%= turbo_stream.append list_id do %>
    <script>
        (function(){
            var chk = document.getElementById('<%= modal_id %>');
            if (chk) chk.checked = false;
            var container = document.createElement('div');
            container.innerHTML = "<%= j render(partial: 'partials/alerts') %>";
            while (container.firstChild) { document.body.appendChild(container.firstChild); }
        })();
    </script>
<% end %>
