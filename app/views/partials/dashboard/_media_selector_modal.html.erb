<%
  # Set default values for optional parameters
  title ||= 'Select Media'
  images_only ||= false
  target_input ||= nil
%>

<!-- Media Selector Modal -->
<div id="<%= modal_id %>" class="modal"
     data-controller="media-selector"
     data-media-selector-target-input-value="<%= target_input %>"
     data-media-selector-images-only-value="<%= images_only %>"
     data-media-selector-modal-id-value="<%= modal_id %>">
  <div class="modal-box w-11/12 max-w-5xl">
    <div class="flex justify-between items-center mb-4">
      <h3 class="font-bold text-lg"><%= title %></h3>
      <button
        type="button"
        class="btn btn-sm btn-circle btn-ghost"
        data-action="click->media-selector#closeModal">
        ✕
      </button>
    </div>

    <!-- Media Library Content -->
    <div class="media-selector-content">

      <!-- Upload Section -->
      <div class="mb-6 p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
        <div class="text-center">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-12 h-12 mx-auto text-gray-400 mb-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18.75 19.5H6.75Z" />
          </svg>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Upload a new image or select from existing media</p>
          <div class="flex gap-2 justify-center">
            <label class="btn btn-primary btn-sm">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 16.5V9.75m0 0 3 3m-3-3-3 3M6.75 19.5a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18.75 19.5H6.75Z" />
              </svg>
              Upload New
              <input type="file"
                     accept="<%= images_only ? 'image/*' : '*' %>"
                     class="hidden"
                     data-media-selector-target="fileInput"
                     data-action="change->media-selector#uploadFile">
            </label>
          </div>
        </div>

        <!-- Upload Progress -->
        <div class="mt-4 hidden" data-media-selector-target="uploadProgress">
          <div class="flex items-center gap-2">
            <span class="loading loading-spinner loading-sm"></span>
            <span class="text-sm">Uploading...</span>
          </div>
          <progress class="progress progress-primary w-full mt-2" data-media-selector-target="progressBar"></progress>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="flex flex-col sm:flex-row gap-4 mb-6">
        <div class="flex-1">
          <input
            type="text"
            placeholder="Search media..."
            class="input input-bordered w-full"
            data-media-selector-target="searchInput"
            data-action="input->media-selector#search">
        </div>
        <div class="flex gap-2">
          <select
            class="select select-bordered"
            data-media-selector-target="typeFilter"
            data-action="change->media-selector#filterByType">
            <option value="">All Types</option>
            <option value="image">Images</option>
            <option value="video">Videos</option>
            <option value="audio">Audio</option>
            <option value="document">Documents</option>
          </select>
        </div>
      </div>

      <!-- Loading State -->
      <div class="text-center py-8 hidden" data-media-selector-target="loading">
        <span class="loading loading-spinner loading-lg"></span>
        <p class="mt-2 text-gray-600 dark:text-gray-400">Loading media...</p>
      </div>

      <!-- Media Grid -->
      <div
        class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto min-h-[200px]"
        data-media-selector-target="mediaGrid"
        style="min-height: 200px;">
        <!-- Media items will be loaded here -->
      </div>

      <!-- Empty State -->
      <div class="text-center py-8 hidden" data-media-selector-target="emptyState">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 mx-auto text-gray-400 mb-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
        </svg>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">No media found</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          <% if images_only %>
            No images available in your media library.
          <% else %>
            No media files found matching your criteria.
          <% end %>
        </p>
        <a href="<%= dashboard_media_library_index_path %>" class="btn btn-primary" target="_blank">
          Go to Media Library
        </a>
      </div>
    </div>

    <!-- Modal Actions -->
    <div class="modal-action">
      <button
        type="button"
        class="btn"
        data-action="click->media-selector#closeModal">
        Cancel
      </button>
    </div>
  </div>
</div>

<!-- Include the confirm modal for delete functionality -->
<div data-controller="confirm">
  <%= render "partials/dashboard/confirm" %>
</div>
