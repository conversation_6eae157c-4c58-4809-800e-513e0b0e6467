<% if resource.errors.any? %>
  <div role="alert" class="border-s-4 border-error-content bg-error p-4">
    <div class="flex items-center gap-2 text-error-content">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5">
        <path
          fill-rule="evenodd"
          d="M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z"
          clip-rule="evenodd"
        />
      </svg>

      <strong class="font-medium"><%= t('activerecord.errors.template.header', count: resource.errors.count, model: resource.class.model_name.human) %></strong>
    </div>
    <ol class="list-decimal pl-4">
      <% resource.errors.each do |error| %>
          <li><%= error.full_message %></li>
      <% end %>
    </ol>
  </div>
<% end %>
