<%# 
  Reusable partial for destroy modal turbo stream responses
  
  Required locals:
  - resource: the destroyed resource instance (e.g., @tag, @category)
  - list_id: the ID of the tbody/container where the row existed (e.g., "tags", "categories")
%>

<%# Remove the destroyed resource from the list %>
<%= turbo_stream.remove resource %>

<%# Inject the rendered alerts partial into the page %>
<%= turbo_stream.append list_id do %>
    <script>
        (function(){
            var container = document.createElement('div');
            container.innerHTML = "<%= j render(partial: 'partials/alerts') %>";
            while (container.firstChild) { document.body.appendChild(container.firstChild); }
        })();
    </script>
<% end %>
