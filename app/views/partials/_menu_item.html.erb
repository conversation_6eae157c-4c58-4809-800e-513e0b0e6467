<%# locals: { item: MenuItem, children: Array } %>
<% children_data = local_assigns.fetch(:children, []) %>
<% use_details_summary = local_assigns.fetch(:use_details_summary, true) %>

<% if children_data.any? %>
  <% if use_details_summary %>
    <li>
      <details>
        <summary><%= item.name %></summary>
        <ul class="p-2 bg-base-100 rounded-t-none">
          <% children_data.each do |child_item, grandchildren| %>
            <%= render "partials/menu_item", item: child_item, children: grandchildren, use_details_summary: use_details_summary %>
          <% end %>
        </ul>
      </details>
    </li>
  <% else %>
    <li>
      <a><%= item.name %></a>
      <ul class="p-2">
        <% children_data.each do |child_item, grandchildren| %>
          <%= render "partials/menu_item", item: child_item, children: grandchildren, use_details_summary: use_details_summary %>
        <% end %>
      </ul>
    </li>
  <% end %>
<% else %>
  <%
    link_path = case item.linkable_type
                when "Category"
                  category_path(item.linkable) if item.linkable
                when "Tag"
                  tag_path(item.linkable) if item.linkable
                else # "URL" or nil
                  item.url
                end
  %>
  <li><%= link_to item.name, (link_path if link_path.present?) %></li>
<% end %>