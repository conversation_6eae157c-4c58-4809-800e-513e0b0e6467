<% menu_classes = local_assigns.fetch(:menu_class, "menu menu-horizontal px-1") %>
<% use_details_summary = local_assigns.fetch(:use_details_summary, true) %>
<ul class="<%= menu_classes %>" tabindex="<%= local_assigns.fetch(:tabindex, nil) %>">
  <% if local_assigns.key?(:menu) && menu.present? %>
    <% menu.build_menu_tree.each do |item, children| %>
      <%= render "partials/menu_item", item: item, children: children, use_details_summary: use_details_summary %>
    <% end %>
  <% end %>
</ul>
