<div class="navbar bg-base-100 shadow-sm sticky top-0 z-10">
  <div class="navbar-start">
    <div class="dropdown">
      <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" /> </svg>
      </div>
      <%= render_menu_for(:header, menu_class: "menu menu-lg dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow", tabindex: 0, use_details_summary: false) %>
    </div>
    <%= link_to (@site_name || "Rorschools"), root_path, class: "btn btn-ghost text-xl" %>
  </div>
  <div class="navbar-center hidden lg:flex">
    <%= render_menu_for(:header) %>
  </div>
  <div class="navbar-end">
    <%= form_with url: posts_path, method: :get, local: true, data: { searchDelayValue: 300, controller: "search", turbo_frame: "posts"} do |f| %>
      <%= f.text_field :query, value: params[:query], placeholder: "Search", autocomplete: "off", data: { action: "input->search#submit" }, class: "input input-bordered w-24 md:w-auto" %>      
    <% end %>
    <%= render partial: "partials/language_switch" %>
    <%= render 'partials/theme_controller', light_theme: @application_light_theme , dark_theme: @application_dark_theme %>
    <% if user_signed_in? %>
      <%= link_to translate_page_title(:dashboard), dashboard_root_path, class: "btn btn-ghost" %>
    <% end %>
  </div>
</div>