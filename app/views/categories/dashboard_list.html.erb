<div class="card bg-base-100 mt-4 shadow-xl">
    <div class="card-body overflow-x-auto">        
        <div class="card-actions">
            <h1 class="text-2xl font-bold mb-4"><%= translate_resource_heading(:category, :dashboard_list) %></h1>
            <label for="new_category_modal" class="btn btn-dash btn-primary"><%= translate_action(:add, Category) %></label>
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th><%= translate_field(:name) %></th>                    
                    <th><%= translate_field(:description) %></th>
                    <th class="text-right"><%= translate_field(:actions) %></th>
                </tr>
            </thead>
            <tbody id="categories">
                <% @categories.each do |cat| %>
                    <tr id="<%= dom_id(cat) %>">
                        <td><%= cat.name %></td>
                        <td><%= cat.description %></td>
                        <td class="text-right">
                            <div class="join">
                                <%= link_to translate_action(:show), cat, class: "btn btn-dash btn-primary btn-sm join-item", target: "_blank" %>
                                <button type="button" class="btn btn-dash btn-secondary btn-sm join-item" data-action="click->modal-remote#open" data-url="<%= edit_dashboard_category_path(cat, modal: 1) %>">
                                    <%= translate_action(:edit) %>
                                </button>
                                <button type="button" class="btn btn-dash btn-error btn-sm join-item" data-action="click->confirm#open" data-url="<%= dashboard_category_path(cat) %>" data-method="delete" data-name="<%= cat.name %>">
                                    <%= translate_action(:delete) %>
                                </button>
                            </div>
                        </td>
                    </tr>
                <% end %>
            </tbody>
        </table>
        <%= render "partials/pagination", pagy: @pagy if @pagy.pages > 1 %>
    </div>
</div>

<%= render "partials/dashboard/new_modal",
    modal_id: "new_category_modal",
    resource_name: "category",
    resource_instance: @category %>