<%= turbo_frame_tag dom_id(category, :form) do %>
  <%= form_with(model: [ :dashboard, category ]) do |form| %>
    <%= render "partials/dashboard/form_error", resource: category %>
    <div>
      <%= form.label :name, translate_field(:name), class: "label" %>
      <%= form.text_field :name, class: "input input-bordered w-full" %>
    </div>
    <div class="mt-2">
      <%= form.label :description, translate_field(:description), class: "label" %>  
      <%= form.textarea :description, class: "textarea textarea-bordered w-full" %>
    </div>

    <div class="modal-action">
      <%= form.submit translate_action(:save), class: "btn btn-primary" %>
    </div>
  <% end %>
<% end %>
