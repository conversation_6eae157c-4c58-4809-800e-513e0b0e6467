<%= form_with(model: page, url: page.persisted? ? dashboard_page_path(page) : dashboard_pages_path, class: "contents") do |form| %>
  <% if page.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(page.errors.count, "error") %> prohibited this page from being saved:</h2>

      <ul>
        <% page.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :title, style: "display: block" %>
    <%= form.text_field :title %>
  </div>

  <div>
    <%= form.label :slug, style: "display: block" %>
    <%= form.text_field :slug %>
  </div>

  <div>
    <%= form.label :body, style: "display: block" %>
    <%= form.textarea :body %>
  </div>

  <div>
    <%= form.label :published, style: "display: block" %>
    <%= form.checkbox :published %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
