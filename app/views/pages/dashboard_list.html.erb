<div class="card bg-base-100 mt-4 shadow-xl">
    <div class="card-body overflow-x-auto">
        <div class="card-actions">
            <h1 class="text-2xl font-bold mb-4"><%= translate_resource_heading(:page, :dashboard_list) %></h1>
            <%= link_to translate_resource_heading(:page, :new), new_dashboard_page_path, class: "btn btn-outline btn-dash btn-primary" %>
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th><%= translate_field(:title) %></th>                    
                    <th><%= translate_field(:published) %></th>
                    <th><%= translate_field(:published_at) %></th>
                    <th><%= translate_field(:author) %></th>
                    <th><%= translate_field(:actions) %></th>
            </thead>
            <tbody>
                <% @pages.each do |page| %>
                    <tr>
                        <td>
                            <% if page.title.length > 20 %>
                                <span class="tooltip tooltip-primary tooltip-right" data-tip="<%= page.title %>"><%= truncate(page.title, length: 20) %></span>
                            <% else %>
                                <%= page.title %>
                            <% end %>
                        </td>                        
                        <td><%= translate_boolean(page.published?) %></td>
                        <td><%= localize_datetime(page.published_at) %></td>
                        <td><%= page.user.email %></td>
                        <td class="flex gap-2">
                            <%= link_to translate_action(:show), page, class: "btn btn-dash btn-primary", target: "_blank" %>
                            <%= link_to translate_action(:edit), edit_dashboard_page_path(page), class: "btn btn-dash btn-secondary" %>
                            <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= dashboard_page_path(page) %>" data-method="delete" data-name="<%= page.title %>">
                                <%= translate_action(:delete) %>
                            </button>
                        </td>
                    </tr>
                <% end %>
            </tbody>
        </table>      
        <%= render "partials/pagination", pagy: @pagy if @pagy.pages > 1 %>  
    </div>        
</div>