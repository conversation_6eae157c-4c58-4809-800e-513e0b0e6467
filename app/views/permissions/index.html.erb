<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
  <%# Form to create a new permission %>
  <div class="lg:col-span-1">
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Add New Permission</h2>
        <%= form_with(model: [ :dashboard, @permission ], class: "space-y-4") do |form| %>
          <% if @permission.errors.any? %>
            <div role="alert" class="alert alert-error">
              <span><%= @permission.errors.full_messages.to_sentence %></span>
            </div>
          <% end %>

          <div>
            <%= form.label :role, class: "label" %>
            <%= form.select :role, options_for_select(@roles, @permission.role), { include_blank: "Select a role" }, class: "select select-bordered w-full" %>
          </div>

          <div>
            <%= form.label :action, class: "label" %>
            <%= form.select :action, options_for_select(@actions, @permission.action), { include_blank: "Select an action" }, class: "select select-bordered w-full" %>
          </div>

          <div>
            <%= form.label :subject_class, "Resource (Model)", class: "label" %>
            <%= form.select :subject_class, options_for_select(@models, @permission.subject_class), { include_blank: "Select a resource" }, class: "select select-bordered w-full" %>
          </div>

          <div class="card-actions justify-end">
            <%= form.submit "Add Permission", class: "btn btn-primary" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <%# List of existing permissions %>
  <div class="lg:col-span-2">
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Existing Permissions</h2>
        <div class="space-y-6">
          <% @permissions_by_role.each do |role, permissions| %>
            <div>
              <h3 class="text-lg font-bold capitalize border-b mb-2 pb-1"><%= role %></h3>
              <ul class="list-disc list-inside space-y-1">
                <% permissions.each do |permission| %>
                  <li class="flex items-center justify-between">
                    <span>Can <strong><%= permission.action %></strong> on <strong><%= permission.subject_class %></strong></span>
                    <%= button_to "Delete", dashboard_permission_path(permission), method: :delete, class: "btn btn-xs btn-error btn-outline", form: { data: { turbo_confirm: "Are you sure?" } } %>
                  </li>
                <% end %>
              </ul>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
