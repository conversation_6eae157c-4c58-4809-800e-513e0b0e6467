<div class="sm:w-1/2 mx-auto" data-controller="settings-form">
    <div class="text-center">
        <h2 class="text-3xl font-bold text-base-content"><%= translate_page_title(:settings) %></h2>
        <div class="tabs tabs-box mt-2">
            <% if can?(:update, :settings) %>   
            <input type="radio" name="setting_tabs" class="tab" aria-label="<%= translate_page_element(:settings, :site_settings) %>" <%= 'checked' if @active_tab == 'site' %> />
            <div class="tab-content bg-base-100 border-2 border-dashed border-base-300 p-6">
                <div data-controller="theme-preview">
                    <%= form_with scope: :setting, url: dashboard_settings_path, method: :patch, data: { action: "submit->settings-form#clearThemeCache" } do |form| %>                        
                        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                            <div class="mt-2">
                                <%= form.label :site_name, translate_page_element(:settings, :site_name), class: "block text-sm font-medium mb-1 text-left"  %>
                                <%= form.text_field :site_name, value: @site_name, class: "input input-primary w-full" %>
                            </div>
                            <div class="sm:mt-2">
                                <%= form.label :site_description, translate_page_element(:settings, :site_description), class: "block text-sm font-medium mb-1 text-left"  %>
                                <%= form.text_field :site_description, value: @site_description, class: "input input-primary w-full validator", maxlength: 160, title: t('flash.warnings.max_desc')%>
                                <p class="validator-hint"><%= t('flash.warnings.max_desc') %></p>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                            <div class="mt-2">
                                <%= form.label :site_default_locale, translate_page_element(:settings, :site_default_locale), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :site_default_locale, options_for_select(available_locales_with_names.invert, @site_default_locale), {}, { class: "select select-primary w-full" } %>
                            </div>
                            <div class="sm:mt-2">
                                <%= form.label :site_default_time_zone, translate_page_element(:settings, :site_default_time_zone), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :site_default_time_zone, time_zone_options_for_select(@site_default_time_zone), {}, { class: "select select-primary w-full" } %>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                            <div class="mt-2">
                                <%= form.label :site_light_theme, translate_page_element(:settings, :light_theme), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :site_light_theme, options_for_select(@light_theme_list, @site_light_theme), { prompt: translate_page_element(:settings, :pick_light_theme) }, { class: "select select-primary w-full", data: { theme_preview_target: "lightSelect", action: "change->theme-preview#update" } } %>
                            </div>
                            <div class="sm:mt-2">
                                <%= form.label :site_dark_theme, translate_page_element(:settings, :dark_theme), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :site_dark_theme, options_for_select(@dark_theme_list, @site_dark_theme), { prompt: translate_page_element(:settings, :pick_dark_theme) }, { class: "select select-primary w-full", data: { theme_preview_target: "darkSelect", action: "change->theme-preview#update" } } %>
                            </div>
                        </div>                        
                        <%= render 'partials/theme_preview', light_theme: @site_light_theme, dark_theme: @site_dark_theme %>
                        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                            <div class="mt-2">
                                <%= form.label :header_menu_id, translate_page_element(:settings, :header_menu), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :header_menu_id, options_from_collection_for_select(@menus, :id, :name, @header_menu_id), { include_blank: true }, { class: "select select-primary w-full" } %>
                            </div>
                            <div class="sm:mt-2">
                                <%= form.label :footer_menu_id, translate_page_element(:settings, :footer_menu), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :footer_menu_id, options_from_collection_for_select(@menus, :id, :name, @footer_menu_id), { include_blank: true }, { class: "select select-primary w-full" } %>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                            <div class="mt-2">
                                <%= form.label :footer_name, translate_page_element(:settings, :footer_name), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.text_field :footer_name, value: @footer_name, class: "input input-primary w-full", maxlength: 50 %>
                            </div>
                            <div class="sm:mt-2">
                                <%= form.label :footer_year, translate_page_element(:settings, :footer_year), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.text_field :footer_year, value: @footer_year, class: "input input-primary w-full", pattern: "\\d{4}", title: "Year must be 4 digits", maxlength: 4 %>
                            </div>
                        </div>
                        <div class="mt-2">
                            <%= form.label :footer_desc, translate_page_element(:settings, :footer_desc), class: "block text-sm font-medium mb-1 text-left" %>
                            <%= form.text_field :footer_desc, value: @footer_desc, class: "input input-primary w-full", maxlength: 255 %>
                        </div>

                        <%= form.submit translate_page_element(:settings, :save_site_settings), class: "btn btn-dash btn-primary mt-4" %>
                    <% end %>                        
                </div>
            </div>
            <% end %>

            <input type="radio" name="setting_tabs" class="tab" aria-label="<%= translate_page_element(:settings, :user_settings) %>" <%= 'checked' if @active_tab == 'user' %> />
            <div class="tab-content bg-base-100 border-base-300 p-6">
                <div data-controller="theme-preview">
                    <%= form_with scope: :setting, url: dashboard_settings_path, method: :patch, data: { action: "submit->settings-form#clearThemeCache" } do |form| %>                        
                        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-8">
                            <div class="mt-2">
                                <%= form.label :light_theme, translate_page_element(:settings, :light_theme), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :light_theme, options_for_select(@light_theme_list, @user_light_theme), { prompt: translate_page_element(:settings, :pick_light_theme) }, { class: "select select-primary w-full", data: { theme_preview_target: "lightSelect", action: "change->theme-preview#update" } } %>
                            </div>
                            <div class="sm:mt-2">
                                <%= form.label :dark_theme, translate_page_element(:settings, :dark_theme), class: "block text-sm font-medium mb-1 text-left" %>
                                <%= form.select :dark_theme, options_for_select(@dark_theme_list, @user_dark_theme), { prompt: translate_page_element(:settings, :pick_dark_theme) }, { class: "select select-primary w-full", data: { theme_preview_target: "darkSelect", action: "change->theme-preview#update" } } %>
                            </div>
                        </div>
                        <div class="mt-2">
                            <%= form.label :time_zone, translate_page_element(:settings, :time_zone), class: "block text-sm font-medium mb-1 text-left" %>
                            <%= form.select :time_zone, time_zone_options_for_select(@user_time_zone), {}, { class: "select select-primary w-full" } %>
                        </div>
                        <%= render 'partials/theme_preview', light_theme: @user_light_theme, dark_theme: @user_dark_theme %>
                        <%= form.submit translate_page_element(:settings, :save_user_settings), class: "btn btn-dash btn-primary mt-4" %>
                    <% end %>                    
                </div>
            </div> 

           <input type="radio" name="setting_tabs" class="tab" aria-label="<%= translate_page_element(:settings, :system) %>" <%= 'checked' if @active_tab == 'system' %> />      
           <div class="tab-content bg-base-100 border-base-300 p-6">
                <div class="text-left">
                    <h3 class="text-lg font-bold mb-4">Application Environment</h3>
                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <tbody>
                                <tr class="hover">
                                    <th class="w-1/3">Ruby Version</th>
                                    <td><%= RUBY_VERSION %></td>
                                </tr>
                                <tr class="hover">
                                    <th>Rails Version</th>
                                    <td><%= Rails.version.to_s %></td>
                                </tr>
                                <tr class="hover">
                                    <th>Database Adapter</th>
                                    <td><%= ActiveRecord::Base.connection.adapter_name %></td>
                                </tr>
                                <tr class="hover">
                                    <th>Image Processor</th>
                                    <td><%= Rails.application.config.active_storage.variant_processor %> <span class="text-xs text-base-content/70">(via ActiveStorage)</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
           </div>      
        </div>                                                                                    
    </div>
</div>