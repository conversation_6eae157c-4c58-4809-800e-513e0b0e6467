<div class="card bg-base-100 mt-4">
  <div class="card-body overflow-x-auto">
    <div class="card-actions">
      <h1 class="text-2xl font-bold mb-4"><%= translate_resource_heading(:user, :index) %></h1>
      <% if can? :create, User %>
        <button class="btn btn-dash btn-primary" onclick="my_modal_1.showModal()"><%= translate_action(:add, User) %></button>
      <% end %>
    </div>    
    <table class="table">
        <thead>
            <tr>
                <th>No</th>
                <th><%= translate_attribute(User, :email) %></th>
                <th><%= translate_attribute(User, :role) %></th>
                <th><%= translate_field(:created_at) %></th>
                <% if can? :create, User %>
                  <th><%= translate_field(:actions) %></th>
                <% end %>
            </tr>
        </thead>
        <tbody>
            <% @users.each_with_index do |user, index| %>
                <tr>
                    <td><%= @pagy.from + index %></td> 
                    <td><%= user.email %></td>       
                    <td><%= user.role.capitalize %></td>   
                    <td><%= localize_datetime(user.created_at) %></td>
                    <% if can? :create, User %>
                      <td class="flex gap-2">
                        <% if can? :update, user %>
                            <button class="btn btn-dash btn-success" onclick="edit_modal_<%= user.id %>.showModal()"><%= translate_action(:edit) %></button>
                        <% end %>
                        <% if can? :destroy, user %>
                            <button type="button" class="btn btn-dash btn-error" data-action="click->confirm#open" data-url="<%= dashboard_path(user) %>" data-method="delete" data-name="<%= user.email %>">
                              <%= translate_action(:delete) %>
                            </button>
                        <% end %>
                      </td>
                    <% end %>
                </tr>                
                <% if can? :update, user %>
                    <dialog id="edit_modal_<%= user.id %>" class="modal">
                      <div class="modal-box w-11/12 max-w-lg">
                        <h3 class="text-lg font-bold"><%= translate_resource_heading(:user, :edit) %></h3>
                            <%= form_with(model: user, url: dashboard_d_user_path(user), method: :patch) do |f| %>
                                <%= f.email_field :email, class: "input input-primary w-full mt-2", placeholder: translate_form_placeholder(User, :email, default: "Email") %>
                                <%= f.select :role, options_for_select([["Viewer", "viewer"], ["Admin", "admin"], ["Editor", "editor"], ["Author", "author"]], user.role), { include_blank: translate_form_placeholder(User, :role, suffix: 'select', default: "Select a role") }, { class: "select select-primary w-full mt-2" } %>
                                <p class="text-sm text-gray-500 mt-2"><%= t('flash.warnings.leave_password_blank') %></p>
                                <%= f.password_field :password, class: "input input-primary w-full mt-2", placeholder: translate_form_placeholder(User, :new_password) %>
                                <%= f.password_field :password_confirmation, class: "input input-primary w-full mt-2", placeholder: translate_form_placeholder(User, :password_confirmation) %>
                                <%= f.submit translate_action(:save), class: "btn btn-dash btn-primary mt-2" %>
                            <% end %>
                        <div class="modal-action">
                          <form method="dialog">
                            <button class="btn"><%= translate_action(:cancel) %></button>
                          </form>
                        </div>
                      </div>
                    </dialog>
                <% end %>
            <% end %>
        </tbody>
    </table>
    <%= render "partials/pagination", pagy: @pagy if @pagy.pages > 1 %>  
  </div>
</div>
<% if can? :create, User %>
    <dialog id="my_modal_1" class="modal">
      <div class="modal-box w-11/12 max-w-lg">
        <h3 class="text-lg font-bold"><%= translate_resource_heading(:user, :new) %></h3>
            <%= form_with(model: @user, url: dashboard_users_path, method: :post) do |f| %>
                <%= f.email_field :email, class: "input input-primary w-full mt-2", placeholder: translate_form_placeholder(User, :email, default: "Email") %>
                <%= f.select :role, options_for_select([["Viewer", "viewer"], ["Admin", "admin"], ["Editor", "editor"], ["Author", "author"]]), { include_blank: translate_form_placeholder(User, :role, suffix: 'select', default: "Select a role") }, { class: "select select-primary w-full mt-2" } %>
                <%= f.password_field :password, class: "input input-primary w-full mt-2", placeholder: translate_form_placeholder(User, :password) %>
                <%= f.password_field :password_confirmation, class: "input input-primary w-full mt-2", placeholder: translate_form_placeholder(User, :password_confirmation) %>
                <%= f.submit translate_action(:add), class: "btn btn-dash btn-primary mt-2" %>
            <% end %>
        <div class="modal-action">
          <form method="dialog">                
            <button class="btn"><%= translate_action(:cancel) %></button>
          </form>
        </div>
      </div>
    </dialog>
<% end %>
