<h1 class="text-2xl font-bold mb-4"><%= translate_page_title(:dashboard, :index) %></h1>

<div class="my-8 p-4 card bg-base-100 shadow-sm" data-controller="analytics-chart" data-analytics-chart-url-value="<%= dashboard_analytics_data_path %>">
  <div class="flex flex-col sm:flex-row justify-between items-start overflow-x-auto p-4">
    <h2 class="text-xl font-semibold mb-4"><%= translate_page_title(:dashboard, :pageviews) %></h2>
    <div class="flex items-center space-x-2 -mt-1">
      <div>
        <label for="start_date" class="sr-only">Start Date</label>
        <input type="date" id="start_date" data-analytics-chart-target="startDate" class="input">
      </div>
      <span class="text-gray-500">to</span>
      <div>
        <label for="end_date" class="sr-only">End Date</label>
        <input type="date" id="end_date" data-analytics-chart-target="endDate" class="input">
      </div>
      <div>
        <button data-action="click->analytics-chart#refreshChart" class="inline-flex justify-center btn btn-dash btn-primary">
          <%= translate_action(:refresh) %>
        </button>
      </div>
    </div>
  </div>

  <!-- Daisyui Skeleton -->
  <div data-analytics-chart-target="skeleton">
    <div class="skeleton h-96 w-full mt-4"></div>
    <div class="mt-8">
      <div class="skeleton h-7 w-32 mb-4"></div>
      <div class="overflow-x-auto rounded-box border border-base-content/5 bg-base-100">
        <table class="min-w-full table">
          <thead class="bg-primary text-primary-content">
            <tr>
              <th class="px-6 py-3"><div class="skeleton h-4 w-24 bg-primary-content/20"></div></th>
              <th class="px-6 py-3"><div class="skeleton h-4 w-24 bg-primary-content/20"></div></th>
              <th class="px-6 py-3"><div class="skeleton h-4 w-16 bg-primary-content/20"></div></th>
            </tr>
          </thead>
          <tbody>
            <% 5.times do %>
              <tr>
                <td><div class="skeleton h-4 w-4/5"></div></td>
                <td><div class="skeleton h-4 w-16"></div></td>
                <td><div class="skeleton h-4 w-16"></div></td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  
  <div data-analytics-chart-target="content" class="hidden">
    <div class="relative h-96 mt-4">
      <canvas data-analytics-chart-target="canvas"></canvas>
    </div>
    <div class="mt-8">
      <h3 class="text-lg font-medium leading-6"><%= translate_page_title(:dashboard, :top_pages) %></h3>
      <div class="mt-4 overflow-x-auto rounded-box border border-base-content/5 bg-base-100">
        <table class="min-w-full table">
          <thead class="bg-primary text-primary-content">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"><%= translate_page_title(:dashboard, :page_path) %></th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"><%= translate_page_title(:dashboard, :pageviews) %></th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"><%= translate_model(User, count: 2) %></th>
            </tr>
          </thead>
          <tbody data-analytics-chart-target="tableBody">
            
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
