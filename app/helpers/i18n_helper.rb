# frozen_string_literal: true

module I18nHelper
  # Common translation patterns
  def translate_action(action, model = nil)
    t("common.actions.#{action}", model: model&.model_name&.human)
  end

  def translate_field(field)
    t("common.fields.#{field}")
  end

  def translate_common(key, subkey = nil)
    t("common.#{key}.#{subkey}")
  end

  def translate_confirmation(type = :default, item: nil)
    case type
    when :delete
      t("common.confirmations.delete_confirmation", item: item)
    else
      t("common.confirmations.are_you_sure")
    end
  end

  # Resource-specific translations
  def translate_resource_heading(resource, action)
    t(key_for_resource_heading(resource, action))
  end

  def translate_page_title(page, section = :index)
    t(key_for_page_title(page, section))
  end

  # Generic page element translations
  def translate_page_element(page, element, section: :index)
    t("pages.#{page}.#{section}.#{element}")
  end

  # Flash message helpers
  def translate_flash_notice(action, resource = nil)
    if resource
      model_name = t("activerecord.models.#{resource}", default: resource.to_s.classify)
      t("flash.notices.#{action}", model: model_name)
    else
      t("flash.notices.#{action}")
    end
  end

  def translate_flash_error(action, resource = nil)
    if resource
      model_name = t("activerecord.models.#{resource}", default: resource.to_s.classify)
      t("flash.errors.#{action}", model: model_name)
    else
      t("flash.errors.#{action}")
    end
  end

  # Navigation helpers
  def translate_navigation(section, item)
    t("navigation.#{section}.#{item}")
  end

  # Model attribute translation with fallback
  def translate_attribute(model, attribute)
    t(key_for_model_attribute(model, attribute),
      default: t("common.fields.#{attribute}", default: attribute.to_s.humanize))
  end

  # Model name translation
  def translate_model(model, count: 1)
    model.model_name.human(count: count)
  end

  # Authorization messages
  def translate_authorization_error(type = :access_denied)
    t("authorization.#{type}.message")
  end

  # Date and time formatting with locale
  def localize_datetime(datetime, format: :default)
    return "" if datetime.blank?

    l(datetime, format: format)
  end

  # Get available locales with human names
  def available_locales_with_names
    {
      en: "English",
      id: "Bahasa Indonesia"
    }
  end

  # Boolean translation
  def translate_boolean(value)
    value ? t("common.responses.yes") : t("common.responses.no")
  end

  # Form helper integration
  def translate_form_label(model, attribute)
    translate_attribute(model, attribute)
  end

  def translate_form_placeholder(model, attribute, suffix: "placeholder", default: "")
    key = "activerecord.attributes.#{model.model_name.i18n_key}.#{attribute}_#{suffix}"
    t(key, default: default)
  end

  private

  # Key Generation Logic
  # Centralizes how translation keys are built, making future changes easy.
  def key_for_resource_heading(resource, action)
    "resources.#{resource.to_s.pluralize}.#{action}.heading"
  end

  def key_for_resource_title(resource, action)
    "resources.#{resource.to_s.pluralize}.#{action}.title"
  end

  def key_for_page_title(page, section = :index)
    "pages.#{page}.#{section}.title"
  end

  def key_for_flash_notice(action, resource = nil)
    "flash.notices.#{action}"
  end

  def key_for_flash_error(action, resource = nil)
    "flash.errors.#{action}"
  end

  def key_for_model_attribute(model, attribute)
    model_name = model.is_a?(Class) ? model.model_name.i18n_key : model.class.model_name.i18n_key
    "activerecord.attributes.#{model_name}.#{attribute}"
  end
end
