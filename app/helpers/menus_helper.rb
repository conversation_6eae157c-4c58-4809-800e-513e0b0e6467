module <PERSON>us<PERSON>el<PERSON>
  def render_menu_for(location, options = {})
    menu_id = Setting.find_by(key: "#{location}_menu_id")&.value

    return if menu_id.blank?

    menu = Menu.includes(:menu_items).find_by(id: menu_id)

    return if menu.nil? || menu.menu_items.empty?

    items_by_type = menu.menu_items.group_by(&:linkable_type)

    items_by_type.each do |type, items|
      next unless type.in?(%w[Category Tag])
      ActiveRecord::Associations::Preloader.new(records: items, associations: :linkable).call
    end

    render partial: "partials/menu", locals: { menu: menu }.merge(options)
  end
end
