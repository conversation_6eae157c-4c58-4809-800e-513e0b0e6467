class ApplicationController < ActionController::Base
  include Pagy::Method
  include SettingsHelper
  include I18nHelper
  helper I18n<PERSON>elper
  before_action :configure_permitted_parameters, if: :devise_controller?
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  before_action :set_themes
  allow_browser versions: :modern

  # Switch locale based on params[:locale] https://guides.rubyonrails.org/i18n.html#managing-the-locale-across-requests
  around_action :switch_locale
  around_action :set_time_zone

  # Use different layout for devise controllers
  layout :layout_by_resource

  def switch_locale(&action)
    locale = params[:locale] || detect_locale || @site_default_locale&.to_sym || I18n.default_locale
    I18n.with_locale(locale, &action)
  end

  # Setting the Locale from URL Params
  def default_url_options
    { locale: I18n.locale }
  end

  # Detect locale from browser
  def detect_locale
     preferred_languages = request.env["HTTP_ACCEPT_LANGUAGE"]&.scan(/\w{2}/)&.map(&:downcase)
     available = I18n.available_locales.map(&:to_s)
     detected_locale = preferred_languages&.find { |lang| available.include?(lang) }
     detected_locale&.to_sym
  end

  def layout_by_resource
    if devise_controller? && (action_name == "new" || action_name == "create")
      "auth"
    elsif devise_controller? && (action_name == "edit" || action_name == "update")
      "dashboard"
    else
      "application"
    end
  end

  def after_sign_in_path_for(resource)
    if resource.viewer?
      root_path(locale: detect_locale)
    else
      dashboard_root_path(locale: detect_locale)
    end
  end

  def after_sign_up_path_for(resource)
    after_sign_in_path_for(resource)
  end

  rescue_from CanCan::AccessDenied do |exception|
    respond_to do |format|
      format.json { render nothing: true, status: :forbidden }
      format.js   { render nothing: true, status: :forbidden }
      if exception.message == I18n.t("authorization.access_denied.message", locale: :en)
        format.html { redirect_to access_denied_path(locale: detect_locale) }
      else
        format.html { redirect_to access_denied_path(locale: detect_locale, message: exception.message) }
      end
    end
  end

  rescue_from GoogleAnalyticsService::ApiError do |exception|
    render json: { error: exception.message }, status: :internal_server_error
  end

  rescue_from ActiveRecord::RecordNotFound, with: :not_found

  protected

  def set_time_zone(&action)
    # Use the user's time zone if they are logged in, otherwise use the site's default.
    # These instance variables are set in the `set_themes` before_action.
    time_zone = current_user ? @user_time_zone : @site_default_time_zone
    Time.use_zone(time_zone, &action)
  end

  def not_found
    I18n.with_locale(params[:locale] || I18n.default_locale) do
      render "errors/not_found", status: :not_found, layout: "error"
    end
  end

  def set_themes
    settings = Setting.get_multiple({
      "site_light_theme" => "silk",
      "site_dark_theme" => "black",
      "site_name" => "Rorschools",
      "site_default_locale" => "id",
      "site_default_time_zone" => "Asia/Jakarta"
    })

    @application_light_theme = settings["site_light_theme"]
    @application_dark_theme = settings["site_dark_theme"]
    @site_default_locale = settings["site_default_locale"]
    @site_name = settings["site_name"]
    @site_default_time_zone = settings["site_default_time_zone"]

    if current_user.present?
      user_settings = UserSetting.get_multiple(current_user, {
        "light_theme" => "winter",
        "dark_theme" => "dark",
        "time_zone" => "Asia/Jakarta"
      })
      @user_light_theme = user_settings["light_theme"]
      @user_dark_theme = user_settings["dark_theme"]
      @user_time_zone = user_settings["time_zone"]
    else
      @user_light_theme = @application_light_theme
      @user_dark_theme = @application_dark_theme
    end
  end

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [ :avatar, :avatar_media_item_id ])
    devise_parameter_sanitizer.permit(:account_update, keys: [ :avatar, :avatar_media_item_id ])
  end
end
