class Dashboard::MediaLibraryController < ApplicationController
  before_action :authenticate_user!
  before_action :set_media_item, only: [ :show, :update, :destroy ]
  before_action :authorize_media_access, only: [ :show, :update, :destroy ]
  before_action :set_breadcrumbs
  layout "dashboard"

  def index
    base_query = current_user.admin? ? MediaItem.all : current_user.media_items
    filtered_items = filter_media_items(base_query).recent

    respond_to do |format|
      format.html do
        @pagy, @media_items = pagy(filtered_items.with_attached_file, limit: 24)
      end
      format.json do
        @pagy, @media_items = pagy(filtered_items.with_attached_file.preload(:user), limit: 24) # Keep for JSON
        render json: media_items_json(@media_items)
      end
    end
  end

  def show
    respond_to do |format|
      format.json { render json: media_item_json(@media_item) }
    end
  end

  def create
    @media_item = current_user.media_items.build(media_item_params)

    if @media_item.save
      respond_to do |format|
        format.json { render json: media_item_json(@media_item), status: :created }
        format.html { redirect_to dashboard_media_library_index_path, notice: translate_flash_notice(:created, :media_item) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @media_item.errors.full_messages }, status: :unprocessable_entity }
        format.html { redirect_to dashboard_media_library_index_path, alert: @media_item.errors.full_messages.join(", ") }
      end
    end
  end

  def update
    Rails.cache.delete("media_item_json/#{@media_item.id}/#{@media_item.updated_at.to_i}")
    Rails.cache.delete(@media_item)

    if @media_item.update(media_item_update_params)
      respond_to do |format|
        format.json { render json: media_item_json(@media_item) }
        format.html { redirect_to dashboard_media_library_index_path, notice: translate_flash_notice(:updated, :media_item) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @media_item.errors.full_messages }, status: :unprocessable_entity }
        format.html { redirect_to dashboard_media_library_index_path, alert: @media_item.errors.full_messages.join(", ") }
      end
    end
  end

  def destroy
    Rails.cache.delete("media_item_json/#{@media_item.id}/#{@media_item.updated_at.to_i}")
    Rails.cache.delete(@media_item)

    @media_item.destroy
    respond_to do |format|
      format.turbo_stream do
        flash.now[:notice] = translate_flash_notice(:destroyed, :media_item)
      end
      format.json { head :no_content }
      format.html { redirect_to dashboard_media_library_index_path, notice: translate_flash_notice(:destroyed, :media_item) }
    end
  end

  def upload
    unless params[:file].present?
      render json: { error: t("pages.media_library.no_file_provided") }, status: :unprocessable_entity
      return
    end

    @media_item = current_user.media_items.build
    @media_item.file.attach(params[:file])

    if @media_item.save
      render json: media_item_json(@media_item), status: :created
    else
      render json: { errors: @media_item.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def set_breadcrumbs
    helpers.add_breadcrumb I18n.t("pages.dashboard.index.title"), dashboard_root_path
    helpers.add_breadcrumb I18n.t("pages.media_library.index.title"), dashboard_media_library_index_path
  end

  def set_media_item
    @media_item = MediaItem.find(params[:id])
  end

  def authorize_media_access
    unless current_user.admin? || @media_item.user == current_user
      render json: { error: t("pages.media_library.access_denied") }, status: :forbidden
    end
  end

  def media_item_params
    params.require(:media_item).permit(:title, :alt_text, :description, :file)
  end

  def media_item_update_params
    params.require(:media_item).permit(:title, :alt_text, :description)
  end

  def filter_media_items(items)
    items = items.where("title ILIKE ?", "%#{params[:search]}%") if params[:search].present?
    if params[:type].present?
      Rails.logger.debug "[MediaLibrary] Filtering by type: #{params[:type]}"
      if params[:type].end_with?("/")
        items = items.where("content_type LIKE ?", "#{params[:type]}%")
      else
        items = items.where(content_type: params[:type])
      end
    end
    items = items.images if params[:images_only] == "true"
    items
  end

  def media_items_json(items)
    {
      media_items: items.map { |item| media_item_json(item) },
      pagination: {
        current_page: @pagy.page,
        total_pages: @pagy.pages,
        total_count: @pagy.count
      }
    }
  end

  def media_item_json(item)
    Rails.cache.fetch(item .cache_key_with_version) do
      {
        id: item.id,
        title: item.title,
        alt_text: item.alt_text,
        description: item.description,
        original_filename: item.original_filename,
        content_type: item.content_type,
        file_size: item.file_size,
        file_size_human: item.file_size_human,
        is_image: item.image?,
        width: item.width,
        height: item.height,
        dimensions: item.dimensions,
        file_url: item.file_url,
        thumbnail_url: item.thumbnail_url,
        created_at: item.created_at.iso8601,
        user: {
          id: item.user.id,
          email: item.user.email
        }
      }
    end
  end
end
