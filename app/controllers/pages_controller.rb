class PagesController < ApplicationController
  before_action :authorize_pages, only: [ :dashboard_list, :create, :update, :destroy ]
  before_action :authenticate_user!, except: [ :index, :show ]
  before_action :set_page, only: %i[ show edit update destroy ]
  before_action :set_pages_breadcrumbs, except: [ :index, :show ]
  layout "dashboard", except: [ :index, :show ]

  def dashboard_list
    authorize! :read, Page
    set_meta_tags title: translate_resource_heading(:page, :dashboard_list)
    @pagy, @pages = pagy(Page.order(created_at: :desc).includes(:user), limit: 10)
    @page = Page.new
  end

  # GET /pages or /pages.json
  def index
    @pages = Page.all
  end

  # GET /pages/1 or /pages/1.json
  def show
    if request.path != page_path(@page)
      redirect_to @page, status: :moved_permanently
    end
  end

  # GET /pages/new
  def new
    @page = Page.new
  end

  # GET /pages/1/edit
  def edit
    authorize! :update, @page
  end

  # POST /pages or /pages.json
  def create
    authorize! :create, Page
    @page = Page.new(page_params)
    @page.user = current_user

    respond_to do |format|
      if @page.save
        format.html { redirect_to @page, notice: translate_flash_notice(:created, :page) }
        format.json { render :show, status: :created, location: @page }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @page.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /pages/1 or /pages/1.json
  def update
    authorize! :update, @page
    respond_to do |format|
      if @page.update(page_params)
        format.html { redirect_to @page, notice: translate_flash_notice(:updated, :page), status: :see_other }
        format.json { render :show, status: :ok, location: @page }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @page.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /pages/1 or /pages/1.json
  def destroy
    authorize! :destroy, @page
    @page.destroy!

    respond_to do |format|
      format.html { redirect_to dashboard_pages_path, notice: translate_flash_notice(:destroyed, :page), status: :see_other }
      format.json { head :no_content }
    end
  end

  private
    def authorize_pages
      authorize! :read, Page
    end
    # Use callbacks to share common setup or constraints between actions.
    def set_page
      @page = Page.friendly.find(params[:slug])
    end

    # Only allow a list of trusted parameters through.
    def page_params
      params.require(:page).permit(:title, :slug, :body, :published, :featured_image, :featured_media_item_id)
    end

    def set_pages_breadcrumbs
      helpers.add_breadcrumb translate_page_title(:dashboard), dashboard_root_path
      if action_name == "dashboard_list"
        helpers.add_breadcrumb translate_resource_heading(:page, :dashboard_list), nil
      else
        helpers.add_breadcrumb translate_resource_heading(:page, :dashboard_list), dashboard_pages_path
      end

      case action_name
      when "new", "create"
        helpers.add_breadcrumb translate_resource_heading(:page, :new), nil
      when "edit", "update"
        helpers.add_breadcrumb @page.title, nil if @page
      end
    end
end
