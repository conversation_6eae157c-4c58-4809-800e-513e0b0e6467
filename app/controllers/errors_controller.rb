class ErrorsController < ApplicationController
  layout "error"
  def access_denied
    @message = params[:message] || translate_authorization_error(:access_denied)
    @code = params[:code] || 403
    respond_to do |format|
      format.html { render :access_denied, status: :forbidden }
      format.json { render json: { error: @message }, status: :forbidden }
      format.js   { render nothing: true, status: :forbidden }
    end
  end

  def not_found
    render status: :not_found
  end
end
