class TagsController < ApplicationController
  before_action :authorize_tags, only: [ :dashboard_list, :create, :update, :destroy ]
  before_action :authenticate_user!, except: [ :index, :show ]
  before_action :set_tag, only: %i[ show edit update destroy ]
  before_action :set_tags_breadcrumbs, except: [ :index, :show ]
  layout "dashboard", except: [ :index, :show ]

  def dashboard_list
    authorize! :read, Tag
    set_meta_tags title: translate_resource_heading(:tag, :dashboard_list)
    @pagy, @tags = pagy(Tag.order(created_at: :desc), limit: 10)
    @tag = Tag.new
  end

  # GET /tags or /tags.json
  def index
    @tags = Tag.all
  end

  # GET /tags/1 or /tags/1.json
  def show
    @tag = Tag.friendly.find(params[:slug])
    @posts = @tag.posts.published.order(published_at: :desc)
  end

  # GET /tags/new
  def new
    @tag = Tag.new
  end

  # GET /tags/1/edit
  def edit
    # If requested as a modal (modal=1) or via XHR, render only the form partial
    if params[:modal].present? || request.xhr?
      render partial: "form", locals: { tag: @tag }
    end
  end

  # POST /tags or /tags.json
  def create
    @tag = Tag.new(tag_params)

    respond_to do |format|
      if @tag.save
        format.turbo_stream do
          flash.now[:notice] = translate_flash_notice(:created, :tag)
        end
        format.html { redirect_to dashboard_tags_path, notice: translate_flash_notice(:created, :tag), status: :see_other }
        format.json { render :show, status: :created, location: @tag }
      else
        format.turbo_stream do
          render turbo_stream: turbo_stream.replace(helpers.dom_id(@tag, :form), partial: "form", locals: { tag: @tag })
        end
        format.html { render :new, status: :unprocessable_entity, layout: "dashboard" }
        format.json { render json: @tag.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /tags/1 or /tags/1.json
  def update
    respond_to do |format|
      if @tag.update(tag_params)
        format.turbo_stream do
          flash.now[:notice] = translate_flash_notice(:updated, :tag)
          render :update
        end
        format.html { redirect_to dashboard_tags_path, notice: translate_flash_notice(:updated, :tag), status: :see_other }
        format.json { render :show, status: :ok, location: @tag }
      else
        format.turbo_stream { render turbo_stream: turbo_stream.replace(helpers.dom_id(@tag, :form), partial: "form", locals: { tag: @tag }) }
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @tag.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /tags/1 or /tags/1.json
  def destroy
    @tag.destroy!

    respond_to do |format|
      format.turbo_stream do
        flash.now[:notice] = translate_flash_notice(:destroyed, :tag)
      end
      format.html { redirect_to dashboard_tags_path, notice: translate_flash_notice(:destroyed, :tag), status: :see_other }
      format.json { head :no_content }
    end
  end

  private
    def authorize_tags
      authorize! :read, Tag
    end
    # Use callbacks to share common setup or constraints between actions.
    def set_tag
      @tag = Tag.friendly.find(params[:slug])
    end

    # Only allow a list of trusted parameters through.
    def tag_params
      params.require(:tag).permit(:name)
    end

    def set_tags_breadcrumbs
      helpers.add_breadcrumb translate_page_title(:dashboard), dashboard_root_path
      if action_name == "dashboard_list"
        helpers.add_breadcrumb translate_resource_heading(:tag, :dashboard_list), nil
      else
        helpers.add_breadcrumb translate_resource_heading(:tag, :dashboard_list), dashboard_categories_path
      end

      case action_name
      when "new", "create"
        helpers.add_breadcrumb translate_resource_heading(:tag, :new), nil
      when "edit", "update"
        helpers.add_breadcrumb @tag.name, nil if @tag
      end
    end
end
