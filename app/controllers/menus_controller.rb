class MenusController < ApplicationController
  before_action :authorize_menus
  before_action :authenticate_user!
  before_action :set_menu_with_items, only: [ :show ]
  before_action :set_menu, only: [ :edit, :update, :destroy ]
  before_action :set_menus_breadcrumbs, only: [ :index, :new, :show ]
  layout "dashboard"
  def index
    helpers.add_breadcrumb translate_resource_heading(:menu, :index), nil
    @menus = Menu.all
    @menu = Menu.new
  end

  def show
    helpers.add_breadcrumb translate_resource_heading(:menu, :index), dashboard_menus_path
    helpers.add_breadcrumb translate_resource_heading(:menu, :new), nil
    @menu_item = MenuItem.new
  end

  def new
    helpers.add_breadcrumb translate_resource_heading(:menu, :index), nil
    @menu = Menu.new
  end

  def edit
    if params[:modal].present? || request.xhr?
      render partial: "form", locals: { menu: @menu }
    end
  end

  def create
    @menu = Menu.new(menu_params)
    respond_to do |format|
      if @menu.save
        format.turbo_stream do
          flash.now[:notice] = translate_flash_notice(:created, :menu)
          render :create
        end
        format.html { redirect_to dashboard_menus_path, notice: translate_flash_notice(:created, :menu) }
        format.json { render :show, status: :created, location: @menu }
      else
        format.turbo_stream do
          render turbo_stream: turbo_stream.replace(helpers.dom_id(@menu, :form), partial: "form", locals: { menu: @menu })
        end
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @menu.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    respond_to do |format|
      if @menu.update(menu_params)
        format.turbo_stream do
          flash.now[:notice] = translate_flash_notice(:updated, :menu)
          render :update
        end
        format.html { redirect_to dashboard_categories_path, notice: translate_flash_notice(:updated, :menu) }
        format.json { render :show, status: :ok, location: @menu }
      else
        format.turbo_stream { render turbo_stream: turbo_stream.replace(helpers.dom_id(@menu, :form), partial: "form", locals: { menu: @menu }) }
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @menu.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @menu.destroy!
    respond_to do |format|
      format.turbo_stream do
        flash.now[:notice] = translate_flash_notice(:destroyed, :menu)
      end
      format.html { redirect_to dashboard_menus_path, notice: translate_flash_notice(:destroyed, :menu), status: :see_other }
      format.json { head :no_content }
    end
  end

  private

  def set_menus_breadcrumbs
    helpers.add_breadcrumb translate_page_title(:dashboard), dashboard_root_path
  end

  def authorize_menus
    authorize! :read, Menu
  end

  def set_menu
    @menu = Menu.find(params[:id])
  end

  def set_menu_with_items
    @menu = Menu.find(params[:id])
  end

  def menu_params
    params.require(:menu).permit(:name)
  end
end
