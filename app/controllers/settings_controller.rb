class SettingsController < ApplicationController
  before_action :authorize_settings_update, only: [ :update ]
  before_action :authorize_settings, only: [ :index ]
  before_action :authenticate_user!
  before_action :set_settings_breadcrumbs
  layout "dashboard"
  include SettingsHelper

  def index
    load_settings_for_view
  end

  def update
    is_site_setting_update = setting_params.keys.any? { |k| k.start_with?("site_") || k.start_with?("footer_") || k.end_with?("_menu_id") }
    active_tab = is_site_setting_update ? "site" : "user"

    # --- Validation Start ---
    errors = []
    if setting_params[:footer_year].present? && !setting_params[:footer_year].match?(/^\d{4}$/)
      errors << "Footer Year must be a 4-digit number."
    end
    if setting_params[:footer_name].present? && setting_params[:footer_name].length > 50
      errors << "Footer Name cannot be longer than 50 characters."
    end
    if setting_params[:footer_desc].present? && setting_params[:footer_desc].length > 255
      errors << "Footer Description cannot be longer than 255 characters."
    end
    # --- Validation End ---

    if errors.any?
      flash.now[:alert] = errors.join(" ")
      repopulate_view_with_params
      return render :index, status: :unprocessable_entity
    end

    setting_params.each do |key, value|
      if key.starts_with?("site_") || key.starts_with?("footer_") || key.end_with?("_menu_id")
        update_option(key, value)
      else
        update_user_option(current_user, key, value)
      end
    end

    set_themes

    respond_to do |format|
      format.html { redirect_to dashboard_settings_path(active_tab: active_tab), notice: translate_flash_notice(:updated, :settings) }
      format.json { head :ok }
    end
  end

  private

  def repopulate_view_with_params
    # First, load all the current settings from the database
    load_settings_for_view

    # Then, overwrite them with the submitted (invalid) params to repopulate the form
    setting_params.each do |key, value|
      # The time zone from params doesn't have the 'Asia/' prefix, so handle it separately
      if key == "site_default_time_zone"
        @site_default_time_zone = value
      elsif key == "time_zone"
        @user_time_zone = value
      elsif instance_variable_defined?("@#{key}")
        instance_variable_set("@#{key}", value)
      end
    end
  end

  def load_settings_for_view
    set_meta_tags title: translate_page_title(:settings)

    default_tab = current_user.admin? ? "site" : "user"
    @active_tab = params[:active_tab] || default_tab
    @active_tab = default_tab unless [ "site", "user" ].include?(@active_tab) && (current_user.admin? || @active_tab != "site")

    site_settings = get_options({
      "site_name" => "Rorschools",
      "site_description" => "A school website",
      "site_light_theme" => "winter",
      "site_dark_theme" => "black",
      "site_default_locale" => "id",
      "site_default_time_zone" => "Asia/Jakarta",
      "footer_name" => "Rorschools",
      "footer_year" => Time.current.year.to_s,
      "footer_desc" => "A school website"
    })

    @site_name = site_settings["site_name"]
    @site_description = site_settings["site_description"]
    @site_light_theme = site_settings["site_light_theme"]
    @site_dark_theme = site_settings["site_dark_theme"]
    @site_default_locale = site_settings["site_default_locale"]
    @site_default_time_zone = site_settings["site_default_time_zone"].sub(/^Asia\//, "")
    @footer_name = site_settings["footer_name"]
    @footer_year = site_settings["footer_year"]
    @footer_desc = site_settings["footer_desc"]

    user_settings = get_user_options(current_user, { "light_theme" => "winter", "dark_theme" => "black", "time_zone" => "Asia/Jakarta" })
    @user_light_theme = user_settings["light_theme"]
    @user_dark_theme = user_settings["dark_theme"]
    @user_time_zone = user_settings["time_zone"].sub(/^Asia\//, "")
    @light_theme_list = [ "light", "cupcake", "bumblebee", "emerald", "corporate", "retro", "cyberpunk", "valentine", "garden", "aqua", "lofi", "pastel", "fantasy", "wireframe", "cmyk", "autumn", "acid", "lemonade", "winter", "nord", "caramellatte", "silk" ]
    @dark_theme_list = [ "dark", "synthwave", "halloween", "forest", "black", "luxury", "dracula", "business", "night", "coffee", "dim", "sunset", "abyss" ]
    @menus = Menu.all
    @header_menu_id = Setting.find_by(key: "header_menu_id")&.value
    @footer_menu_id = Setting.find_by(key: "footer_menu_id")&.value
  end

  def authorize_settings_update
    if setting_params.keys.any? { |k| k.start_with?("site_") || k.start_with?("footer_") }
      authorize! :update, :settings
    else
      authorize! :update, :user_settings
    end
  end

  def authorize_settings
    authorize! :read, :settings
  end

  def setting_params
    params.require(:setting).permit(
      :site_name, :site_description, :site_light_theme, :site_dark_theme, :site_default_locale, :site_default_time_zone,
      :light_theme, :dark_theme, :time_zone, :header_menu_id, :footer_menu_id,
      :footer_name, :footer_year, :footer_desc
    )
  end

  def set_settings_breadcrumbs
    helpers.add_breadcrumb translate_page_title(:dashboard), dashboard_root_path
    helpers.add_breadcrumb translate_page_title(:settings), dashboard_settings_path
  end
end
