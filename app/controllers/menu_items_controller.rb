class MenuItemsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_menu

  def create
    @menu_item = @menu.menu_items.new(menu_item_params)
    @menu_item.position = (@menu.menu_items.roots.maximum(:position) || 0) + 1

    respond_to do |format|
      if @menu_item.save
        format.turbo_stream do
          flash.now[:success] = translate_flash_notice(:created, :menu_item)
          render :create
        end
        format.html { redirect_to dashboard_menu_path(@menu), notice: translate_flash_notice(:created, :menu_item) }
        format.json { render :show, status: :created, location: @menu_item }
      else
        format.turbo_stream do
          render turbo_stream: turbo_stream.replace(
            helpers.dom_id(@menu_item, :form),
            partial: "menus/form_content",
            locals: { menu: @menu, menu_item: @menu_item }
          )
        end
        format.html { render "menus/show", status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @menu_item = @menu.menu_items.find(params[:id])
    @menu_item.destroy

    @menu.menu_items.order(:position).each_with_index do |item, index|
      item.update_column(:position, index + 1)
    end

    respond_to do |format|
      format.turbo_stream do
        flash.now[:notice] = translate_flash_notice(:destroyed, :menu_item)
      end
      format.html { redirect_to dashboard_categories_path, notice: translate_flash_notice(:destroyed, :menu_item), status: :see_other }
      format.json { head :no_content }
    end
  end

  def reorder
    MenuItem.transaction do
      update_positions(params[:menu_items])
    end

    head :ok
  end

  private

  def update_positions(menu_items, parent_id = nil)
    return unless menu_items

    menu_items.each_with_index do |item_data, index|
      menu_item = MenuItem.find(item_data[:id])
      menu_item.update!(position: index + 1, parent_id: parent_id)
      update_positions(item_data[:children], item_data[:id]) if item_data[:children]
    end
  end

  def set_menu
    @menu = Menu.find(params[:menu_id])
  end

  def menu_item_params
    permitted_params = params.require(:menu_item).permit(:name, :linkable_type, :url, :category_id, :tag_id)

    case permitted_params[:linkable_type]
    when "Category"
      permitted_params[:linkable_id] = permitted_params.delete(:category_id)
    when "Tag"
      permitted_params[:linkable_id] = permitted_params.delete(:tag_id)
    end

    permitted_params.except(:category_id, :tag_id)
  end
end
