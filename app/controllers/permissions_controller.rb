class PermissionsController < ApplicationController
  before_action :authenticate_user!
  before_action :authorize_management
  before_action :set_categories_breadcrumbs
  layout "dashboard"

  def index
    set_meta_tags title: translate_page_title(:permission)
    helpers.add_breadcrumb translate_page_title(:permission), nil

    @permissions_by_role = Permission.order(:role, :subject_class, :action).group_by(&:role)

    @permission = Permission.new
    @roles = Permission::ROLES.reject { |r| r == "admin" } # Admins have all permissions by default
    @actions = Permission::ACTIONS
    @models = [ "Post", "Category", "Tag", "User", "dashboard", "settings", "user_settings" ].sort
  end

  def create
    @permission = Permission.new(permission_params)
    if @permission.save
      redirect_to dashboard_permissions_path, notice: translate_flash_notice(:created, :permission)
    else
      @permissions_by_role = Permission.order(:role).group_by(&:role)
      @roles = Permission::ROLES.reject { |r| r == "admin" }
      @actions = Permission::ACTIONS
      @models = [ "Post", "Category", "Tag", "User", "dashboard", "settings", "user_settings" ].sort
      flash.now[:alert] = @permission.errors.full_messages.to_sentence
      render :index, status: :unprocessable_entity
    end
  end

  def destroy
    Permission.find(params[:id]).destroy
    redirect_to dashboard_permissions_path, notice: translate_flash_notice(:destroyed, :permission), status: :see_other
  end

  private

  def set_categories_breadcrumbs
    helpers.add_breadcrumb translate_page_title(:dashboard), dashboard_root_path
  end

  def permission_params
    params.require(:permission).permit(:role, :action, :subject_class)
  end

  def authorize_management
    authorize! :manage, Permission
  end
end
