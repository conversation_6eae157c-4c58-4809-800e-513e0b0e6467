class MediaItem < ApplicationRecord
  belongs_to :user
  has_one_attached :file

  validates :title, presence: true, length: { maximum: 255 }
  validates :original_filename, presence: true
  validates :content_type, presence: true
  validates :file_size, presence: true, numericality: { greater_than: 0 }
  validates :storage_key, presence: true, uniqueness: true

  before_validation :set_metadata_from_file, if: -> { file.attached? && storage_key.blank? }
  before_validation :set_default_title, if: -> { title.blank? }
  before_validation :detect_image_type

  validate :validate_file_presence
  validate :validate_file_content_type
  validate :validate_file_size_limits

  scope :images, -> { where(is_image: true) }
  scope :documents, -> { where(is_image: false) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_user, ->(user) { where(user: user) }

  def image?
    is_image
  end

  def file_url
    return nil unless file.attached?
    Rails.application.routes.url_helpers.rails_blob_url(file, only_path: true)
  end

  def thumbnail_url(size: [ 150, 150 ])
    return nil unless image? && file.attached?
    begin
      Rails.application.routes.url_helpers.rails_representation_url(
        file.variant(resize_to_fill: size).processed,
        only_path: true
      )
    rescue => e
      Rails.logger.error "Error generating thumbnail for MediaItem #{id}: #{e.message}"
      file_url
    end
  end

  def file_size_human
    return "0 B" if file_size.blank? || file_size.zero?

    units = %w[B KB MB GB TB]
    size = file_size.to_f
    unit_index = 0

    while size >= 1024 && unit_index < units.length - 1
      size /= 1024.0
      unit_index += 1
    end

    "#{size.round(1)} #{units[unit_index]}"
  end

  def dimensions
    return nil unless image? && width.present? && height.present?
    "#{width} × #{height}"
  end

  private

  def set_metadata_from_file
    return unless file.attached?

    blob = file.blob
    self.original_filename = blob.filename.to_s
    self.content_type = blob.content_type
    self.file_size = blob.byte_size
    self.storage_key = blob.key

    # Extract image dimensions if it's an image
    if blob.content_type&.start_with?("image/") && blob.metadata.present?
      self.width = blob.metadata["width"]
      self.height = blob.metadata["height"]
    end
  end

  def set_default_title
    self.title = original_filename&.split(".")&.first&.humanize || "Untitled"
  end

  def detect_image_type
    self.is_image = content_type&.start_with?("image/") || false
  end

  def validate_file_presence
    errors.add(:file, "must be attached") unless file.attached?
  end

  def validate_file_content_type
    return unless file.attached?

    allowed_types = [
      "image/jpeg", "image/png", "image/gif", "image/webp",
      "video/mp4", "video/webm",
      "audio/mpeg", "audio/wav", "audio/ogg",
      "application/pdf"
    ]

    unless file.content_type.in?(allowed_types)
      errors.add(:file, "is not a permitted file type")
    end
  end

  def validate_file_size_limits
    return unless file.attached?

    is_image_file = file.blob.content_type.start_with?("image/")

    if is_image_file && file.blob.byte_size > 5.megabytes
      errors.add(:file, "size must be less than 5MB for images")
    elsif !is_image_file && file.blob.byte_size > 100.megabytes
      errors.add(:file, "size must be less than 100MB for other file types")
    end
  end
end
