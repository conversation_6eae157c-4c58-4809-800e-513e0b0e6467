class Permission < ApplicationRecord
  ROLES = %w[admin editor author viewer].freeze
  ACTIONS = %w[manage read create update destroy].freeze

  validates :role, presence: true, inclusion: { in: ROLES }
  validates :action, presence: true, inclusion: { in: ACTIONS }
  validates :subject_class, presence: true

  # Ensure a permission is unique for a role, action, and subject
  validates :action, uniqueness: { scope: [ :role, :subject_class ] }

  # You could add a validation to ensure subject_class is a valid model name
  # validate :subject_class_is_a_model
end
