class Page < ApplicationRecord
  extend FriendlyId
  friendly_id :title, use: [ :slugged, :history ]

  has_one_attached :featured_image
  belongs_to :featured_media_item, class_name: "MediaItem", optional: true

  validates :title, presence: true
  validates :body, presence: true

  scope :published, -> { where(published: true) }

  def should_generate_new_friendly_id?
    will_save_change_to_title? || super
  end

  def featured_image_url
    if featured_media_item&.file&.attached?
      Rails.application.routes.url_helpers.rails_blob_url(featured_media_item.file, only_path: true)
    elsif featured_image.attached?
      Rails.application.routes.url_helpers.rails_blob_url(featured_image, only_path: true)
    end
  end

  def featured_image_thumbnail_url(size: [ 300, 200 ])
    if featured_media_item&.image?
      featured_media_item.thumbnail_url(size: size)
    elsif featured_image.attached?
      begin
        Rails.application.routes.url_helpers.rails_representation_url(
          featured_image.variant(resize_to_fill: size).processed,
          only_path: true
        )
      rescue => e
        Rails.logger.error "Error generating thumbnail for Page #{id}: #{e.message}"
        featured_image_url
      end
    end
  end
end
