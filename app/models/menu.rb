class Menu < ApplicationRecord
  has_many :menu_items, -> { order(position: :asc) }, dependent: :destroy
  validates :name, presence: true

  def build_menu_tree
    items = menu_items.group_by(&:parent_id)
    (items[nil] || []).map do |item|
      [ item, build_children(item, items) ]
    end
  end

  private

  def build_children(item, items)
    (items[item.id] || []).map do |child|
      [ child, build_children(child, items) ]
    end
  end
end
