import { Controller } from "@hotwired/stimulus"
import { Chart } from "chart.js/auto"

// Connects to data-controller="analytics-chart"
export default class extends Controller {
  static values = {
    url: String
  }
  static targets = [ "canvas", "startDate", "endDate", "tableBody", "skeleton", "content" ]

  connect() {
    this.chart = null;
    this.setDefaultDates();
    this.fetchDataAndRenderChart()
  }

  disconnect() {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  setDefaultDates() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 29);

    this.endDateTarget.value = endDate.toISOString().split('T')[0];
    this.startDateTarget.value = startDate.toISOString().split('T')[0];
  }

  refreshChart() {
    this.fetchDataAndRenderChart();
  }

  async fetchDataAndRenderChart() {
    this.showSkeleton();
    const url = new URL(this.urlValue, window.location.origin);
    url.searchParams.set('start_date', this.startDateTarget.value);
    url.searchParams.set('end_date', this.endDateTarget.value);

    try {
      const response = await fetch(url)
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "An unknown error occurred." }));
        throw new Error(errorData.error || `Request failed with status ${response.status}`);
      }
      const data = await response.json()

      this.renderChart(data.chart_data);
      this.renderTable(data.table_data);

    } catch (error) {
      console.error("Error fetching analytics data:", error.message)
      this.canvasTarget.getContext('2d').clearRect(0, 0, this.canvasTarget.width, this.canvasTarget.height); // Clear canvas
      this.canvasTarget.parentElement.insertAdjacentHTML('beforeend', `<div class="absolute inset-0 flex items-center justify-center text-center text-gray-500 p-4">Could not load chart: ${error.message}</div>`);
    } finally {
      this.hideSkeleton();
    }
  }

  renderChart(data) {
      if (this.chart) {
        this.chart.data = data;
        this.chart.update();
        return;
      }

      this.chart = new Chart(this.canvasTarget, {
        type: 'line',
        data: data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
        }
      })
  }

  renderTable(data) {
    this.tableBodyTarget.innerHTML = '';

    if (data.length === 0) {
      this.tableBodyTarget.innerHTML = `<tr><td colspan="3" class="px-6 py-4 text-center text-gray-500">No data available for this period.</td></tr>`;
      return;
    }

    data.forEach(row => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td class="text-sm font-medium">
          <a href="https://${window.location.hostname}${row.path}" target="_blank" rel="noopener noreferrer" class="text-primary">${row.path}</a>
        </td>
        <td class="text-sm">${row.pageviews}</td>
        <td class="text-sm">${row.users}</td>
      `;
      this.tableBodyTarget.appendChild(tr);
    });
  }

  showSkeleton() {
    this.skeletonTarget.classList.remove('hidden');
    this.contentTarget.classList.add('hidden');
  }

  hideSkeleton() {
    this.skeletonTarget.classList.add('hidden');
    this.contentTarget.classList.remove('hidden');
  }
}
