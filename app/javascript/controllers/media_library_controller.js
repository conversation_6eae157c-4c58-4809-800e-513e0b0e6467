import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "dropZone", "fileInput", "uploadProgress", "uploadList", "viewerModal", "viewerContent", "mediaGrid", "emptyState", "contentWrapper",
    "searchInput", "typeFilter", "viewToggle"
  ]

  connect() {
    this.isGridView = true
    this.searchTimeout = null
    this.currentPage = 1
  }

  // File Dialog Method
  openFileDialog() {
    this.fileInputTarget.click()
  }

  // Scroll to upload area (for empty state button)
  scrollToUploadArea() {
    this.dropZoneTarget.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
    // Add a subtle highlight effect
    this.dropZoneTarget.classList.add('ring-2', 'ring-primary', 'ring-opacity-50')
    setTimeout(() => {
      this.dropZoneTarget.classList.remove('ring-2', 'ring-primary', 'ring-opacity-50')
    }, 2000)
  }

  // Drag and Drop Handlers
  handleDragOver(event) {
    event.preventDefault()
    this.dropZoneTarget.classList.add("border-primary", "bg-primary", "bg-opacity-5")
  }

  handleDragEnter(event) {
    event.preventDefault()
  }

  handleDragLeave(event) {
    event.preventDefault()
    if (!this.dropZoneTarget.contains(event.relatedTarget)) {
      this.dropZoneTarget.classList.remove("border-primary", "bg-primary", "bg-opacity-5")
    }
  }

  handleDrop(event) {
    event.preventDefault()
    this.dropZoneTarget.classList.remove("border-primary", "bg-primary", "bg-opacity-5")
    
    const files = Array.from(event.dataTransfer.files)
    this.uploadFiles(files)
  }

  handleFileSelect(event) {
    const files = Array.from(event.target.files)
    this.uploadFiles(files)
  }

  // File Upload
  async uploadFiles(files) {
    if (files.length === 0) return

    this.showUploadProgress()
    
    for (const file of files) {
      await this.uploadSingleFile(file)
    }
    
    // Refresh the media grid after uploads
    this.refreshMediaGrid()
  }

  async uploadSingleFile(file) {
    const uploadItem = this.createUploadItem(file)
    this.uploadListTarget.appendChild(uploadItem)

    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch('/dashboard/media/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.updateUploadItemSuccess(uploadItem, data)
      } else {
        const errorData = await response.json()
        this.updateUploadItemError(uploadItem, errorData.errors || ['Upload failed'])
      }
    } catch (error) {
      this.updateUploadItemError(uploadItem, ['Network error'])
    }
  }

  createUploadItem(file) {
    const item = document.createElement('div')
    item.className = 'flex items-center justify-between p-3 rounded-lg border-y-2 border-base-300 border-dashed'
    item.innerHTML = `
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 rounded flex items-center justify-center border-2 border-base-300 border-dashed">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </div>
        <div>
          <p class="text-sm font-medium">${file.name}</p>
          <p class="text-xs">${this.formatFileSize(file.size)}</p>
        </div>
      </div>
      <div class="upload-status flex items-center">
        <div class="w-6 h-6">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
        </div>
      </div>
    `
    return item
  }

  updateUploadItemSuccess(item, data) {
    const statusDiv = item.querySelector('.upload-status')
    statusDiv.innerHTML = `
      <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
    `
  }

  updateUploadItemError(item, errors) {
    const statusDiv = item.querySelector('.upload-status')
    statusDiv.innerHTML = `
      <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    `
    
    // Add error message
    const errorMsg = document.createElement('p')
    errorMsg.className = 'text-xs text-red-500'
    errorMsg.textContent = errors.join(', ')
    statusDiv.appendChild(errorMsg)
  }

  showUploadProgress() {
    this.uploadProgressTarget.classList.remove("hidden")
  }

  resetUploadForm() {
    this.fileInputTarget.value = ''
    this.uploadProgressTarget.classList.add("hidden")
    this.uploadListTarget.innerHTML = ''
  }

  // Media Viewer Modal
  showMediaViewer(event) {
    event.preventDefault();
    const button = event.currentTarget;
    const mediaUrl = button.dataset.mediaUrl;
    const mediaType = button.dataset.mediaType;
    const mediaTitle = button.dataset.mediaTitle;

    // Check if required targets exist
    if (!this.hasViewerModalTarget || !this.hasViewerContentTarget) {
      console.error('Media viewer modal targets not found');
      return;
    }

    let contentHtml = '';

    if (mediaType.startsWith('image/')) {
      contentHtml = `<img src="${mediaUrl}" alt="${mediaTitle}" class="w-full h-auto max-h-[80vh] object-contain">`;
    } else if (mediaType.startsWith('video/')) {
      contentHtml = `<video controls autoplay class="w-full max-h-[80vh]"><source src="${mediaUrl}" type="${mediaType}">Your browser does not support the video tag.</video>`;
    } else if (mediaType.startsWith('audio/')) {
      contentHtml = `
        <div class="p-8 flex flex-col items-center justify-center">
          <h3 class="text-lg font-bold mb-4">${mediaTitle}</h3>
          <audio controls autoplay class="w-full"><source src="${mediaUrl}" type="${mediaType}">Your browser does not support the audio element.</audio>
        </div>
      `;
    } else if (mediaType === 'application/pdf') {
      // Using an iframe for PDF viewing
      contentHtml = `<iframe src="${mediaUrl}" class="w-full h-[80vh]" frameborder="0"></iframe>`;
    } else {
      contentHtml = `
        <div class="p-8 text-center">
          <p>Cannot preview this file type.</p>
          <a href="${mediaUrl}" class="btn btn-primary mt-4" target="_blank" rel="noopener noreferrer">Download File</a>
        </div>
      `;
    }

    this.viewerContentTarget.innerHTML = contentHtml;
    this.viewerModalTarget.showModal();
  }

  closeViewerModal() {
    if (this.hasViewerModalTarget) {
      this.viewerModalTarget.close();
      this.viewerContentTarget.innerHTML = ''; // Clear content to stop media playback
    }
  }

  // Media Actions
  selectMedia(event) {
    event.stopPropagation()
    const mediaId = event.currentTarget.dataset.mediaId
    
    // Dispatch custom event for media selection
    this.dispatch("mediaSelected", { 
      detail: { mediaId: mediaId }
    })
  }

  async deleteMedia(event) {
    event.stopPropagation()
    const mediaId = event.currentTarget.dataset.mediaId
    
    if (!confirm('Are you sure you want to delete this media file?')) {
      return
    }
    
    try {
      const response = await fetch(`/dashboard/media/${mediaId}`, {
        method: 'DELETE',
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })
      
      if (response.ok) {
        this.refreshMediaGrid()
      }
    } catch (error) {
      console.error('Error deleting media:', error)
    }
  }

  async updateMedia(event) {
    event.preventDefault()
    const form = event.target
    const formData = new FormData(form)
    
    try {
      const response = await fetch(form.action, {
        method: 'PATCH',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })
      
      if (response.ok) {
        this.refreshMediaGrid()
        // Show success message
        this.showNotification('Media updated successfully', 'success')
      }
    } catch (error) {
      console.error('Error updating media:', error)
      this.showNotification('Error updating media', 'error')
    }
  }

  // Search and Filter
  search() {
    clearTimeout(this.searchTimeout)
    this.searchTimeout = setTimeout(() => {
      this.refreshMediaGrid()
    }, 300)
  }

  filter() {
    this.refreshMediaGrid()
  }

  changePage(event) {
    event.preventDefault()
    const url = new URL(event.currentTarget.href)
    this.currentPage = url.searchParams.get('page') || 1
    this.refreshMediaGrid({ pushState: true })
  }

  toggleView() {
    this.isGridView = !this.isGridView
    // Implementation for list/grid view toggle
    // This would require additional CSS classes and layout changes
  }

  // Utility Methods
  async refreshMediaGrid({ pushState = false } = {}) {
    const params = new URLSearchParams()

    if (this.searchInputTarget.value) {
      params.append('search', this.searchInputTarget.value)
    }

    if (this.typeFilterTarget.value) {
      params.append('type', this.typeFilterTarget.value)
    }

    if (this.currentPage > 1) {
      params.append('page', this.currentPage)
    }

    try {
      const response = await fetch(`/dashboard/media?${params.toString()}`, {
        headers: {
          'Accept': 'text/html',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })

      if (response.ok) {
        if (pushState) {
          window.history.pushState({}, '', `?${params.toString()}`)
        }

        const html = await response.text()
        // Update the media grid content
        const parser = new DOMParser()
        const doc = parser.parseFromString(html, 'text/html')
        const newContent = doc.querySelector('[data-media-library-target="contentWrapper"]')

        if (newContent) {
          this.contentWrapperTarget.innerHTML = newContent.innerHTML
          this.initializePaginationActions()
          // Manually reconnect Stimulus controllers for the new content
          this.reconnectStimulus()
        }
      }
    } catch (error) {
      console.error('Error refreshing media grid:', error)
    }
  }

  initializePaginationActions() {
    const paginationLinks = this.contentWrapperTarget.querySelectorAll('.pagy-nav a')
    paginationLinks.forEach(link => {
      link.dataset.action = 'click->media-library#changePage'
    })
  }

  reconnectStimulus() {
    // Since the media items use event delegation through data-action attributes,
    // we don't need to reconnect controllers, just ensure the actions are properly set
    // The parent controller (this one) should handle all the delegated events
  }

  copyUrl(event) {
    const url = event.currentTarget.dataset.url
    navigator.clipboard.writeText(window.location.origin + url).then(() => {
      this.showNotification('URL copied to clipboard', 'success')
    })
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  showNotification(message, type = 'info') {
    // Simple notification - you might want to integrate with your existing notification system
    const notification = document.createElement('div')
    notification.className = `alert alert-${type} fixed top-4 right-4 z-50 max-w-sm`
    notification.textContent = message
    document.body.appendChild(notification)
    
    setTimeout(() => {
      notification.remove()
    }, 3000)
  }
}
