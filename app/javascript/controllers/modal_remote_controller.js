import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { url: String }
  static targets = ["content", "checkbox"]

  connect() {
    // no-op
  }

  async open(event) {
    event.preventDefault()
    const url = event.currentTarget.dataset.url || this.urlValue
    if (!url) return

    // show modal checkbox if present
    const checkbox = this.hasCheckboxTarget ? this.checkboxTarget : document.getElementById('remote_modal_toggle')
    if (checkbox) checkbox.checked = true

    // fetch remote content (expect HTML fragment)
    try {
      const resp = await fetch(url, { headers: { 'X-Requested-With': 'XMLHttpRequest', 'Accept': 'text/html' }, credentials: 'same-origin' })
      const text = await resp.text()
      const container = this.hasContentTarget ? this.contentTarget : document.querySelector('#remote_modal_content')
      if (container) container.innerHTML = text
    } catch (err) {
      console.error('Failed to load modal content', err)
    }
  }

  close() {
    const checkbox = this.hasCheckboxTarget ? this.checkboxTarget : document.getElementById('remote_modal_toggle')
    if (checkbox) checkbox.checked = false
  }
}
