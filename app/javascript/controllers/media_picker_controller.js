import { Controller } from "@hotwired/stimulus"

// Generic Media Picker Controller
// This controller can be instantiated with different identifiers for reusability
export default class extends Controller {
  static targets = ["input", "preview", "removeButton"]
  static values = { modalId: String, inputName: String }

  connect() {
    // Listen for media selection events
    document.addEventListener('media-selector:mediaSelected', this.onMediaSelected.bind(this))
  }

  disconnect() {
    document.removeEventListener('media-selector:mediaSelected', this.onMediaSelected.bind(this))
  }

  openModal(event) {
    event.preventDefault()

    if (this.modalIdValue) {
      const modal = document.getElementById(this.modalIdValue)
      if (modal) {
        // Set the target input for the media selector
        const mediaSelector = modal.querySelector('[data-controller*="media-selector"]')
        if (mediaSelector) {
          mediaSelector.dataset.mediaSelectorTargetInputValue = this.inputNameValue
        }

        modal.classList.add('modal-open')
      }
    }
  }

  onMediaSelected(event) {
    try {
      const detail = event.detail || {}
      const media = detail.media || {}
      const targetInputName = detail.targetInput

      // Only handle if the event is for our input name
      const myName = this.inputNameValue
      if (myName && targetInputName !== myName) {
        return
      }

      const id = media.id
      const url = media.thumbnail_url || media.file_url || media.url || ''

      // set hidden input
      if (this.hasInputTarget) {
        this.inputTarget.value = id
      } else if (myName) {
        const field = document.querySelector(`input[name="${myName}"]`)
        if (field) {
          field.value = id
        }
      }

      // update preview
      if (url) {
        if (this.hasPreviewTarget) {
          this.previewTarget.innerHTML = `<div class="w-full max-w-sm mx-auto"><img src="${url}" alt="Selected image preview" class="w-full h-auto rounded-md" /></div>`
        }
      }

      // show remove button
      if (this.hasRemoveButtonTarget) {
        this.removeButtonTarget.style.display = ''
      }

      // close modal (if present)
      if (this.modalIdValue) {
        const modal = document.getElementById(this.modalIdValue)
        if (modal) modal.classList.remove('modal-open')
      }
    } catch (e) {
      console.error('Error handling media-selector:mediaSelected event', e)
    }
  }

  removeImage(event) {
    event.preventDefault()
    
    // Clear the hidden input
    if (this.hasInputTarget) {
      this.inputTarget.value = ''
    } else if (this.inputNameValue) {
      const field = document.querySelector(`input[name="${this.inputNameValue}"]`)
      if (field) field.value = ''
    }

    // Update preview to show empty state
    if (this.hasPreviewTarget) {
      this.previewTarget.innerHTML = `
        <div class="text-gray-500 dark:text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 mx-auto mb-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
          </svg>
          <p class="text-sm">No image selected</p>
        </div>
      `
    }

    // hide remove button
    if (this.hasRemoveButtonTarget) {
      this.removeButtonTarget.style.display = 'none'
    }
  }
}
