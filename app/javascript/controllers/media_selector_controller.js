import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "searchInput", "typeFilter", "mediaGrid", "loading", "emptyState",
    "fileInput", "uploadProgress", "progressBar"
  ]

  static values = {
    targetInput: String,
    imagesOnly: Boolean,
    modalId: String
  }

  connect() {
    this.searchTimeout = null
    this.loadMedia()

    // Add event listener for select buttons
    this.element.addEventListener('click', (event) => {
      if (event.target.classList.contains('select-media-btn')) {
        this.selectMedia(event)
      }
    })

    // Listen for successful deletions to remove items from grid
    document.addEventListener('turbo:before-stream-render', (event) => {
      const streamElement = event.target
      if (streamElement && streamElement.action === 'remove') {
        // If a media item was deleted, we might need to refresh the grid
        // The turbo stream will handle the removal automatically
      }
    })
  }

  // Modal Management
  closeModal() {
    const modal = document.getElementById(this.modalIdValue)
    if (modal) {
      modal.classList.remove('modal-open')
    }
  }

  // Media Loading
  async loadMedia() {
    this.showLoading()
    
    try {
      const params = new URLSearchParams()
      
      if (this.hasSearchInputTarget && this.searchInputTarget.value) {
        params.append('search', this.searchInputTarget.value)
      }
      
      if (this.hasTypeFilterTarget && this.typeFilterTarget.value) {
        params.append('type', this.typeFilterTarget.value)
      }
      
      if (this.imagesOnlyValue) {
        params.append('images_only', 'true')
      }

      const response = await fetch(`/dashboard/media?${params.toString()}`, {
        headers: {
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.renderMediaItems(data.media_items)
      } else {
        this.showError('Failed to load media')
      }
    } catch (error) {
      console.error('Error loading media:', error)
      this.showError('Network error while loading media')
    }
  }

  renderMediaItems(mediaItems) {
    this.hideLoading()

    if (!mediaItems || mediaItems.length === 0) {
      this.showEmptyState()
      return
    }

    this.hideEmptyState()

    const gridHtml = mediaItems.map(item => this.createMediaItemHtml(item)).join('')
    this.mediaGridTarget.innerHTML = gridHtml
  }

  createMediaItemHtml(item) {
    const thumbnailUrl = item.thumbnail_url || item.file_url
    const isImage = item.is_image

    return `
      <div id="media-item-${item.id}" class="bg-base-200 rounded-lg shadow-sm border-2 border-base-300 border-dashed overflow-hidden hover:shadow-xl transition-shadow cursor-pointer group"
           data-media-id="${item.id}"
           data-media-title="${item.title}"
           data-media-thumbnail="${thumbnailUrl}"
           data-media-file-url="${item.file_url}">
        <div class="aspect-square relative">
          ${isImage ?
            `<div class="skeleton w-full h-full"></div><img src="${thumbnailUrl}" alt="${item.title}" class="w-full h-full object-cover absolute top-0 left-0 opacity-0 transition-opacity duration-500" onload="this.classList.remove('opacity-0')">` :
            `<div class="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800">
               <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-12 h-12 text-gray-400">
                 <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
               </svg>
             </div>`
          }

          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div class="flex gap-2">
              <button
                class="btn btn-sm btn-primary select-media-btn"
                data-media-id="${item.id}">
                Select
              </button>
              <button
                type="button"
                class="btn btn-sm btn-error"
                data-action="click->confirm#open"
                data-url="/dashboard/media/${item.id}"
                data-method="delete"
                data-name="${item.title}">
                Delete
              </button>
            </div>
          </div>
        </div>

        <div class="p-3">
          <h4 class="font-medium text-sm truncate" title="${item.title}">${item.title}</h4>
          <p class="text-xs text-gray-500 mt-1">${item.file_size_human}</p>
          ${item.dimensions ? `<p class="text-xs text-gray-500">${item.dimensions}</p>` : ''}
        </div>
      </div>
    `
  }

  // Media Selection
  selectMedia(event) {
    event.preventDefault()
    event.stopPropagation()

    const button = event.target
    const mediaId = button.dataset.mediaId

    // Find the parent media element that contains the data attributes
    const mediaElement = button.closest('.bg-base-200')

    if (!mediaElement) {
      console.error('Could not find media element')
      return
    }

    // Get media item data from data attributes
    const title = mediaElement.dataset.mediaTitle || `Media ${mediaId}`
    const thumbnailUrl = mediaElement.dataset.mediaThumbnail
    const fileUrl = mediaElement.dataset.mediaFileUrl || thumbnailUrl

    // Create media object
    const media = {
      id: mediaId,
      title: title,
      thumbnail_url: thumbnailUrl,
      file_url: fileUrl
    }

    // Dispatch custom event
    document.dispatchEvent(new CustomEvent('media-selector:mediaSelected', {
      detail: {
        media: media,
        targetInput: this.targetInputValue
      }
    }))

    // Close modal
    this.closeModal()
  }

  // File Upload
  async uploadFile(event) {
    const file = event.target.files[0]
    if (!file) return

    // Validate file type if images only
    if (this.imagesOnlyValue && !file.type.startsWith('image/')) {
      alert('Please select an image file.')
      return
    }

    this.showUploadProgress()

    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch('/dashboard/media/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.hideUploadProgress()

        // Auto-select the uploaded media
        this.selectUploadedMedia(data)

        // Refresh the media grid
        this.loadMedia()
      } else {
        const errorData = await response.json()
        this.hideUploadProgress()
        alert(errorData.errors ? errorData.errors.join(', ') : 'Upload failed')
      }
    } catch (error) {
      console.error('Upload error:', error)
      this.hideUploadProgress()
      alert('Network error during upload')
    }

    // Clear the file input
    event.target.value = ''
  }

  selectUploadedMedia(mediaData) {
    // Create media object
    const media = {
      id: mediaData.id,
      title: mediaData.title,
      thumbnail_url: mediaData.thumbnail_url,
      file_url: mediaData.file_url
    }

    // Dispatch custom event
    document.dispatchEvent(new CustomEvent('media-selector:mediaSelected', {
      detail: {
        media: media,
        targetInput: this.targetInputValue
      }
    }))

    // Close modal
    this.closeModal()
  }

  // Search and Filter
  search() {
    clearTimeout(this.searchTimeout)
    this.searchTimeout = setTimeout(() => {
      this.loadMedia()
    }, 300)
  }

  filterByType() {
    this.loadMedia()
  }

  // UI State Management
  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
    if (this.hasMediaGridTarget) {
      this.mediaGridTarget.innerHTML = ''
    }
    this.hideEmptyState()
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
  }

  showEmptyState() {
    if (this.hasEmptyStateTarget) {
      this.emptyStateTarget.classList.remove('hidden')
    }
  }

  hideEmptyState() {
    if (this.hasEmptyStateTarget) {
      this.emptyStateTarget.classList.add('hidden')
    }
  }

  showUploadProgress() {
    if (this.hasUploadProgressTarget) {
      this.uploadProgressTarget.classList.remove('hidden')
    }
  }

  hideUploadProgress() {
    if (this.hasUploadProgressTarget) {
      this.uploadProgressTarget.classList.add('hidden')
    }
  }

  showError(message) {
    this.hideLoading()
    this.mediaGridTarget.innerHTML = `
      <div class="col-span-full text-center py-8">
        <div class="text-red-500 mb-2">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-12 h-12 mx-auto">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
          </svg>
        </div>
        <p class="text-gray-600 dark:text-gray-400">${message}</p>
        <button class="btn btn-sm btn-outline mt-2" data-action="click->media-selector#loadMedia">
          Try Again
        </button>
      </div>
    `
  }
}
