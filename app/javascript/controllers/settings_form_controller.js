import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {    
    if (sessionStorage.getItem("themeUpdated") === "true") {
      sessionStorage.removeItem("themeUpdated");            
    }
  }

  clearThemeCache() {
    localStorage.removeItem("theme");
    localStorage.removeItem("dashboard-theme");
    sessionStorage.setItem("themeUpdated", "true");
  }
}