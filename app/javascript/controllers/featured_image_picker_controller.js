import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "preview", "image", "removeButton"]

  static values = {
    modalId: String,
    inputName: String
  }

  connect() {
    this.onMediaSelected = this.onMediaSelected.bind(this)
    document.addEventListener('media-selector:mediaSelected', this.onMediaSelected)
  }

  disconnect() {
    document.removeEventListener('media-selector:mediaSelected', this.onMediaSelected)
  }

  openModal(event) {
    event && event.preventDefault()
    const modalId = this.modalIdValue
    if (!modalId) return
    const modal = document.getElementById(modalId)
    if (modal) {
      modal.classList.add('modal-open')
      // make sure the media selector controller will know which input to set
    } else {
      console.error(`Modal with id ${modalId} not found`)
    }
  }

  remove(event) {
    event && event.preventDefault()
    if (this.hasInputTarget) {
      this.inputTarget.value = ''
    } else {
      // try to find by name
      const name = this.inputNameValue
      if (name) {
        const field = document.querySelector(`input[name="${name}"]`)
        if (field) field.value = ''
      }
    }

    // clear preview image
    if (this.hasImageTarget) {
      this.imageTarget.removeAttribute('src')
    } else if (this.hasPreviewTarget) {
      this.previewTarget.innerHTML = '<div class="text-sm text-gray-500">No featured image selected.</div>'
    }
  }

  onMediaSelected(event) {
    try {
      const detail = event.detail || {}
      const media = detail.media || {}
      const targetInputName = detail.targetInput

      // Only handle if the event is for our input name
      const myName = this.inputNameValue
      if (myName && targetInputName !== myName) {
        return
      }

      const id = media.id
      const url = media.thumbnail_url || media.file_url || media.url || ''

      // set hidden input
      if (this.hasInputTarget) {
        this.inputTarget.value = id
      } else if (myName) {
        const field = document.querySelector(`input[name="${myName}"]`)
        if (field) {
          field.value = id
        }
      }

      // update preview
      if (url) {
        if (this.hasImageTarget) {
          this.imageTarget.src = url
        } else if (this.hasPreviewTarget) {
          this.previewTarget.innerHTML = `<div class="w-full max-w-sm mx-auto"><img src="${url}" alt="Featured image preview" class="w-full h-auto rounded-md" /></div>`
        }
      }

      // show remove button
      if (this.hasRemoveButtonTarget) {
        this.removeButtonTarget.style.display = ''
      }

      // close modal (if present)
      if (this.modalIdValue) {
        const modal = document.getElementById(this.modalIdValue)
        if (modal) modal.classList.remove('modal-open')
      }
    } catch (e) {
      console.error('Error handling media-selector:mediaSelected event', e)
    }
  }

  removeImage(event) {
    event && event.preventDefault()

    // Clear the hidden input
    if (this.hasInputTarget) {
      this.inputTarget.value = ''
    } else if (this.inputNameValue) {
      const field = document.querySelector(`input[name="${this.inputNameValue}"]`)
      if (field) field.value = ''
    }

    // Update preview to show empty state
    if (this.hasPreviewTarget) {
      this.previewTarget.innerHTML = `
        <div class="text-gray-500 dark:text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-16 h-16 mx-auto mb-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
          </svg>
          <p class="text-sm">No featured image selected</p>
        </div>
      `
    }

    // hide remove button
    if (this.hasRemoveButtonTarget) {
      this.removeButtonTarget.style.display = 'none'
    }
  }
}
