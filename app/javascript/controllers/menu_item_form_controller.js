import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["linkableType", "categorySelect", "tagSelect", "customUrlField"]

  connect() {
    this.toggleFields()
  }

  toggleFields() {
    // Hide all fields first
    this.categorySelectTarget.style.display = 'none'
    this.tagSelectTarget.style.display = 'none'
    this.customUrlFieldTarget.style.display = 'none'

    // Show the appropriate field based on selection
    const selectedType = this.linkableTypeTarget.value
    
    if (selectedType === 'Category') {
      this.categorySelectTarget.style.display = 'block'
    } else if (selectedType === 'Tag') {
      this.tagSelectTarget.style.display = 'block'
    } else if (selectedType === 'Custom URL') {
      this.customUrlFieldTarget.style.display = 'block'
    }
  }
}
