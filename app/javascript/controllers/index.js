// This file is responsible for registering all your Stimulus controllers.
// It's compatible with esbuild and does not use `require.context`.

import { application } from "./application"

import AlertController from "./alert_controller.js"
application.register("alert", AlertController)

import LanguageController from "./language_controller.js"
application.register("language", LanguageController)

import ThemeController from "./theme_controller.js"
application.register("theme", ThemeController)

import TiptapController from "./tiptap_controller.js"
application.register("tiptap", TiptapController)

import HelloController from "./hello_controller.js"
application.register("hello", HelloController)

import ImagePreviewController from "./image_preview_controller.js"
application.register("image-preview", ImagePreviewController)

import AnalyticsChartController from "./analytics_chart_controller.js"
application.register("analytics-chart", AnalyticsChartController)

import CharacterCounterController from "./character_counter_controller.js"
application.register("character-counter", CharacterCounterController)

import ThemePreviewController from "./theme_preview_controller.js"
application.register("theme-preview", ThemePreviewController)

import SettingsFormController from "./settings_form_controller.js"
application.register("settings-form", SettingsFormController)

import ConfirmController from "./confirm_controller.js"
application.register("confirm", ConfirmController)

import ModalRemoteController from "./modal_remote_controller.js"
application.register("modal-remote", ModalRemoteController)

import MenuItemFormController from "./menu_item_form_controller.js"
application.register("menu-item-form", MenuItemFormController)

import MediaLibraryController from "./media_library_controller.js"
application.register("media-library", MediaLibraryController)

import MediaSelectorController from "./media_selector_controller.js"
application.register("media-selector", MediaSelectorController)

import TestController from "./test_controller.js"
application.register("test", TestController)

import FeaturedImagePickerController from "./featured_image_picker_controller.js"
application.register("featured-image-picker", FeaturedImagePickerController)

import MediaPickerController from "./media_picker_controller.js"
application.register("media-picker", MediaPickerController)