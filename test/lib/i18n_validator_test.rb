# frozen_string_literal: true

require "test_helper"
require_relative "../../lib/i18n_validator"

class I18nValidatorTest < ActiveSupport::TestCase
  def setup
    @validator = I18nValidator.new
    @original_locale = I18n.locale
  end

  def teardown
    I18n.locale = @original_locale
  end

  test "validate! returns validation results" do
    result = @validator.validate!

    assert result.key?(:valid)
    assert result.key?(:errors)
    assert result.key?(:warnings)
    assert result.key?(:summary)

    assert_instance_of Array, result[:errors]
    assert_instance_of Array, result[:warnings]
    assert_instance_of Hash, result[:summary]
  end

  test "extract_all_keys returns array of keys" do
    keys = @validator.send(:extract_all_keys, :en)

    assert_instance_of Array, keys
    assert keys.length > 0
    assert_includes keys, "common.actions.add"
    assert_includes keys, "common.actions.edit"
    assert_includes keys, "flash.notices.user_created"
  end

  test "extract_interpolation_variables finds variables correctly" do
    string_with_vars = "Hello %{name}, you have %{count} messages"
    variables = @validator.send(:extract_interpolation_variables, string_with_vars)

    assert_equal [ "count", "name" ], variables
  end

  test "extract_interpolation_variables handles strings without variables" do
    string_without_vars = "Hello world"
    variables = @validator.send(:extract_interpolation_variables, string_without_vars)

    assert_equal [], variables
  end

  test "looks_like_english? detects English text" do
    english_text = "This is an English sentence with common words"
    non_english_text = "Ini adalah kalimat bahasa Indonesia"

    assert @validator.send(:looks_like_english?, english_text)
    assert_not @validator.send(:looks_like_english?, non_english_text)
  end

  test "is_pluralization_hash? detects pluralization hashes" do
    pluralization_hash = { one: "1 item", other: "%{count} items" }
    regular_hash = { title: "Title", description: "Description" }

    assert @validator.send(:is_pluralization_hash?, pluralization_hash)
    assert_not @validator.send(:is_pluralization_hash?, regular_hash)
  end

  test "class method validate! works" do
    result = I18nValidator.validate!

    assert result.key?(:valid)
    assert_instance_of TrueClass, result[:valid] || FalseClass
  end

  test "class method check_key_exists works" do
    assert I18nValidator.check_key_exists("common.actions.add", :en)
    assert I18nValidator.check_key_exists("common.actions.add", :id)
    assert_not I18nValidator.check_key_exists("nonexistent.key", :en)
  end

  test "class method find_missing_keys returns hash" do
    missing_keys = I18nValidator.find_missing_keys(:en)

    assert_instance_of Hash, missing_keys

    # Should have entries for each non-base locale
    I18n.available_locales.each do |locale|
      next if locale == :en
      assert missing_keys.key?(locale)
      assert_instance_of Array, missing_keys[locale]
    end
  end

  test "class method translation_coverage returns coverage info" do
    coverage = I18nValidator.translation_coverage(:en)

    assert coverage.key?(:total_keys)
    assert coverage.key?(:translated_keys)
    assert coverage.key?(:coverage_percentage)

    assert_instance_of Integer, coverage[:total_keys]
    assert_instance_of Integer, coverage[:translated_keys]
    assert_instance_of Float, coverage[:coverage_percentage]

    assert coverage[:total_keys] >= 0
    assert coverage[:translated_keys] >= 0
    assert coverage[:coverage_percentage] >= 0
    assert coverage[:coverage_percentage] <= 100
  end

  test "translation_coverage handles different locales" do
    en_coverage = I18nValidator.translation_coverage(:en)
    id_coverage = I18nValidator.translation_coverage(:id)

    # Both should have some keys
    assert en_coverage[:total_keys] > 0
    assert id_coverage[:total_keys] > 0

    # Coverage should be reasonable
    assert en_coverage[:coverage_percentage] > 80
    assert id_coverage[:coverage_percentage] > 80
  end

  test "validation handles missing translations gracefully" do
    # This test ensures the validator doesn't crash on missing translations
    result = @validator.validate!

    # Should complete without raising errors
    assert_not_nil result
    assert result.key?(:valid)
  end

  test "validation detects interpolation mismatches" do
    # This would require setting up test translations with mismatched interpolations
    # For now, just ensure the method exists and runs
    @validator.send(:validate_interpolation_consistency)

    # Should not raise errors
    assert true
  end

  test "validation summary includes required fields" do
    result = @validator.validate!
    summary = result[:summary]

    assert summary.key?(:total_errors)
    assert summary.key?(:total_warnings)
    assert summary.key?(:locales_checked)
    assert summary.key?(:status)

    assert_instance_of Integer, summary[:total_errors]
    assert_instance_of Integer, summary[:total_warnings]
    assert_instance_of Integer, summary[:locales_checked]
    assert_includes [ "PASS", "FAIL" ], summary[:status]
  end
end
